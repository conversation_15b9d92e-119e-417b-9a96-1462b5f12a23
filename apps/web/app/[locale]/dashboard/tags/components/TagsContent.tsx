"use client"

import { TranslationProvider } from "@/lib/components/translation/TranslationContext"
import { useTranslationContext } from "@/lib/components/translation/TranslationContext"
import { useState, useCallback, useEffect, useMemo } from "react"
import { GameTag, Language, ProjectLanguage } from "@repo/shared-types"
import { fetchGet, fetchPost } from "@repo/utils/react"
import TagEditor from "./TagEditor"
import {
	Input,
	Button,
	DataTable,
	DataTableContent,
	DataTablePagination,
	DataTableToolbar,
	DataTableFilter,
	DataTableFilterField,
	DataTableFilterActions,
	DataTableAction,
	Card,
} from "@repo/ui/components"
import { ColumnDef } from "@tanstack/react-table"
import { Edit, Plus, Tag as TagIcon } from "lucide-react"
import { useTranslations } from "next-intl"
import { toast } from "sonner"
import { useTable } from "@repo/ui/hooks"
import { z } from "zod"

interface TagsContentProps {
	languages: Language[]
	defaultLanguage: ProjectLanguage
}

// 定义过滤条件schema
const filterSchema = z.object({
	search: z.string().optional(),
})

export default function TagsContent({
	languages,
	defaultLanguage,
}: TagsContentProps) {
	const t = useTranslations("Tags")
	const { currentLanguage } = useTranslationContext()
	const [selectedTagId, setSelectedTagId] = useState<string | null>(null)
	const [isEditorOpen, setIsEditorOpen] = useState(false)
	const [tags, setTags] = useState<GameTag[]>([])
	const [isLoading, setIsLoading] = useState(true)
	const [totalItems, setTotalItems] = useState(0)
	const [isCreatingNewTag, setIsCreatingNewTag] = useState(false)

	const translate = useCallback(
		async (selectedLanguages: ProjectLanguage[]) =>
			fetchPost("/api/game-tag/translate", {
				sourceLocale: defaultLanguage,
				targetLocales: selectedLanguages,
				tagId: selectedTagId,
			}),
		[defaultLanguage, selectedTagId],
	)

	// 加载标签数据
	const loadTags = useCallback(async () => {
		setIsLoading(true)
		try {
			const response = await fetchGet<GameTag[]>(
				`/api/game-tag?locale=${currentLanguage}`,
			)
			if (response && Array.isArray(response)) {
				setTags(response)
				setTotalItems(response.length)

				// 如果没有选中的标签且有标签数据，自动选择第一个
				if (!selectedTagId && response.length > 0 && !isEditorOpen) {
					setSelectedTagId(response[0]!.id)
				}
			}
		} catch (error) {
			console.error(t("loadTagError"), error)
			toast.error(t("loadTagError"))
		} finally {
			setIsLoading(false)
		}
	}, [currentLanguage, selectedTagId, isEditorOpen, t])

	// 初始化标签数据和处理语言变化
	useEffect(() => {
		loadTags()
	}, [loadTags, currentLanguage])

	// 处理标签选择
	const handleSelectTag = (tagId: string) => {
		setSelectedTagId(tagId)
		setIsEditorOpen(true)
		setIsCreatingNewTag(false)
	}

	// 处理标签保存后的刷新
	const handleTagSaved = useCallback((tag: GameTag) => {
		// 更新标签列表中的对应标签
		setTags((prevTags) => {
			const tagExists = prevTags.some((t) => t.id === tag.id)
			if (tagExists) {
				return prevTags.map((t) => (t.id === tag.id ? tag : t))
			} else {
				return [...prevTags, tag]
			}
		})
		setIsCreatingNewTag(false)
	}, [])

	// 创建新标签
	const handleCreateTag = useCallback(() => {
		setSelectedTagId(null)
		setIsCreatingNewTag(true)
		setIsEditorOpen(true)
	}, [])

	// 定义表格列
	const columns: ColumnDef<GameTag>[] = useMemo(
		() => [
			// 序号列
			{
				accessorFn: (_, index) => index + 1,
				id: "index",
				header: t("index"),
				cell: ({ row }) => row.index + 1,
				enableSorting: false,
				enableHiding: false,
				size: 80,
				meta: {
					fixed: true,
				},
			},
			// 数据列
			{
				accessorKey: "name",
				header: t("tagName"),
				cell: ({ row }) => {
					const tag = row.original
					return (
						<div
							className={`flex items-center cursor-pointer ${
								selectedTagId === tag.id ? "text-primary font-medium" : ""
							}`}
							onClick={() => handleSelectTag(tag.id)}
						>
							<TagIcon className="w-4 h-4 mr-2 text-muted-foreground" />
							<span>{tag.name || t("unnamed")}</span>
						</div>
					)
				},
			},
			{
				accessorKey: "slug",
				header: t("tagSlug"),
				cell: ({ row }) => (
					<span className="text-muted-foreground">{row.original.slug}</span>
				),
			},
			{
				accessorKey: "count",
				header: t("tagCount"),
				cell: ({ row }) => {
					const count = row.original.count
					return count !== undefined ? (
						<span className="text-xs bg-muted text-muted-foreground px-1.5 py-0.5 rounded-sm">
							{count}
						</span>
					) : null
				},
			},
			// 操作列
			{
				id: "actions",
				header: t("actions"),
				cell: ({ row }) => {
					return (
						<div className="flex gap-2">
							<DataTableAction
								icon={<Edit size={16} />}
								label={t("edit")}
								onClick={() => handleSelectTag(row.original.id)}
							/>
						</div>
					)
				},
				enableSorting: false,
				enableHiding: false,
				size: 100,
				meta: {
					fixed: true,
				},
			},
		],
		[t, selectedTagId],
	)

	// 使用 useTable hook 管理表格状态
	const { table, filterProps, contentProps, paginationProps, toolbarProps } =
		useTable({
			columns,
			data: tags,
			manualPagination: false,
			totalItems,
			initialPageSize: 10,
			isLoading,
			onCreate: handleCreateTag,
			onCreateLabel: t("addTag"),
			onRefresh: loadTags,
		})

	// 处理过滤器提交
	const handleFilterSubmit = (values: z.infer<typeof filterSchema>) => {
		const searchQuery = values.search || ""
		table.setGlobalFilter(searchQuery)
	}

	return (
		<div className="container py-6">
			<TranslationProvider
				languages={languages}
				defaultLanguage={defaultLanguage}
			>
				<div>
					<Card className="p-6">
						<h1 className="text-2xl font-bold mb-6">标签管理</h1>

						<div>
							<DataTable
								table={table}
								isLoading={isLoading}
								noResultsMessage={t("noTags")}
							>
								<DataTableFilter
									schema={filterSchema}
									{...filterProps}
									onSubmit={handleFilterSubmit}
								>
									<DataTableFilterField name="search">
										<Input placeholder={t("searchTags")} />
									</DataTableFilterField>

									<DataTableFilterActions>
										<Button type="submit">{t("search")}</Button>
										<Button type="reset" variant="outline">
											{t("reset")}
										</Button>
									</DataTableFilterActions>
								</DataTableFilter>

								<DataTableToolbar {...toolbarProps}>
									<Button onClick={handleCreateTag}>
										<Plus className="mr-2 h-4 w-4" />
										{t("addTag")}
									</Button>
								</DataTableToolbar>

								<DataTableContent {...contentProps} />

								<DataTablePagination {...paginationProps} />
							</DataTable>
						</div>

						{/* 标签编辑对话框 */}
						<TagEditor
							tagId={selectedTagId}
							isOpen={isEditorOpen}
							isCreating={isCreatingNewTag}
							onClose={() => {
								setIsEditorOpen(false)
								setIsCreatingNewTag(false)
							}}
							onSaved={handleTagSaved}
						/>
					</Card>
				</div>
			</TranslationProvider>
		</div>
	)
}
