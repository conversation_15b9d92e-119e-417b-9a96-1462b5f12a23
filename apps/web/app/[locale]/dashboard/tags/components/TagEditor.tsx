"use client"

import { useEffect, useState } from "react"
import { useTranslations } from "next-intl"
import { useTranslationContext } from "@/lib/components/translation/TranslationContext"
import { TranslationSwitcher } from "@/lib/components/translation/TranslationSwitcher"
import { zodResolver } from "@hookform/resolvers/zod"
import { GameTag } from "@repo/shared-types"
import { IconSelector } from "@/lib/components/icons/IconSelector"
import { ICON_GROUPS } from "@/lib/components/icons/icon-helpers"
import {
	<PERSON><PERSON>,
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogFooter,
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
	Input,
	Textarea,
} from "@repo/ui/components"
import { fetchGet, fetchPost } from "@repo/utils/react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"
import { Tag } from "lucide-react"

interface TagEditorProps {
	tagId: string | null
	isOpen: boolean
	onClose: () => void
	onSaved?: (tag: GameTag) => void
	isCreating?: boolean
}

// 定义表单验证模式
const TagEditorSchema = (t: any) =>
	z.object({
		name: z.string().min(1, { message: t("tagNameRequired") }),
		slug: z.string().min(1, { message: t("tagSlugRequired") }),
		iconName: z.string().optional(),
		description: z.string().optional(),
		metaTitle: z.string().optional(),
		metaDescription: z.string().optional(),
		imageUrl: z.string().optional(),
	})

export default function TagEditor({
	tagId,
	isOpen,
	onClose,
	onSaved,
	isCreating = false,
}: TagEditorProps) {
	const t = useTranslations("Tags")
	const { currentLanguage, defaultLanguage } = useTranslationContext()
	const [tag, setTag] = useState<GameTag | null>(null)
	const [isLoading, setIsLoading] = useState(false)
	const [isSaving, setIsSaving] = useState(false)

	// 判断当前是否为默认语言
	const isDefaultLanguage = currentLanguage === defaultLanguage

	// 创建表单验证模式
	const formSchema = TagEditorSchema(t)

	// 定义表单
	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			name: "",
			slug: "",
			iconName: "",
			description: "",
			metaTitle: "",
			metaDescription: "",
			imageUrl: "",
		},
	})

	// 加载标签数据
	const loadTag = async () => {
		if (!tagId) {
			// 如果是创建新标签，设置默认值
			if (isCreating) {
				setTag(null)
				form.reset({
					name: "",
					slug: "",
					iconName: "",
					description: "",
					metaTitle: "",
					metaDescription: "",
					imageUrl: "",
				})
				setIsLoading(false)
				return
			}
			setTag(null)
			return
		}

		setIsLoading(true)
		try {
			const response = await fetchGet(
				`/api/game-tag/detail?id=${tagId}&locale=${currentLanguage}`,
			)
			if (response) {
				setTag(response)
				form.reset({
					name: response.name || "",
					slug: response.slug || "",
					iconName: response.iconName || "",
					description: response.description || "",
					metaTitle: response.metaTitle || "",
					metaDescription: response.metaDescription || "",
					imageUrl: response.imageUrl || "",
				})
			}
		} catch (error) {
			console.error(t("loadTagError"), error)
			toast.error(t("loadTagError"))
		} finally {
			setIsLoading(false)
		}
	}

	// 当标签ID或语言变化时重新加载数据
	useEffect(() => {
		if (isOpen) {
			loadTag()
		}
	}, [tagId, currentLanguage, isOpen, isCreating])

	// 处理表单提交
	const onSubmit = async (values: z.infer<typeof formSchema>) => {
		setIsSaving(true)
		try {
			let response: GameTag | null = null

			if (isCreating || !tagId) {
				// 创建新标签
				response = await fetchPost("/api/game-tag/create", {
					locale: currentLanguage,
					name: values.name,
					slug: values.slug,
					iconName: values.iconName,
					description: values.description,
					metaTitle: values.metaTitle,
					metaDescription: values.metaDescription,
					imageUrl: values.imageUrl,
				})

				if (response) {
					toast.success(t("createTagSuccess"))
				}
			} else {
				// 更新现有标签
				const payload = {
					id: tagId,
					locale: currentLanguage,
					...values,
				}

				response = await fetchPost(`/api/game-tag/update`, payload)

				if (response) {
					toast.success(t("saveSuccess"))
				}
			}

			if (response) {
				setTag(response)
				if (onSaved) {
					onSaved(response)
				}
				onClose()
			}
		} catch (error) {
			console.error(t("saveError"), error)
			toast.error(t("saveError"))
		} finally {
			setIsSaving(false)
		}
	}

	// 重置表单
	const handleReset = () => {
		if (tag) {
			form.reset({
				name: tag.name || "",
				slug: tag.slug || "",
				iconName: tag.iconName || "",
				description: tag.description || "",
				metaTitle: tag.metaTitle || "",
				metaDescription: tag.metaDescription || "",
				imageUrl: tag.imageUrl || "",
			})
		}
	}

	return (
		<Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
			<DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex justify-between items-center">
						<span>{isCreating ? t("createTag") : t("editTag")}</span>
					</DialogTitle>
					<TranslationSwitcher  />
				</DialogHeader>

				{isLoading ? (
					<div className="py-12 text-center">
						<div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
						<p className="text-muted-foreground">{t("loading")}</p>
					</div>
				) : (
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
							{/* 基本信息 */}
							<div className="space-y-4">
								{!isCreating && (
									<div className="flex items-center space-x-2 bg-muted/30 p-2 rounded-md">
										<Tag className="h-4 w-4 text-muted-foreground" />
										<div className="text-sm text-muted-foreground">
											{t("currentTag")}:{" "}
											<span className="font-medium text-foreground">
												{tag?.name || t("unnamed")}
											</span>
										</div>
									</div>
								)}

								<div className="grid md:grid-cols-2 gap-6">
									<FormField
										control={form.control}
										name="name"
										render={({ field }) => (
											<FormItem>
												<FormLabel>{t("tagName")}</FormLabel>
												<FormControl>
													<Input placeholder={t("enterTagName")} {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="iconName"
										render={({ field }) => (
											<FormItem>
												<FormLabel>{t("tagIcon")}</FormLabel>
												<FormControl>
													<IconSelector
														value={field.value || ""}
														onChange={field.onChange}
														groups={ICON_GROUPS}
														variant="dialog"
														allowUpload
													/>
												</FormControl>
											</FormItem>
										)}
									/>
								</div>

								<FormField
									control={form.control}
									name="slug"
									render={({ field }) => (
										<FormItem>
											<FormLabel>{t("tagSlug")}</FormLabel>
											<FormControl>
												<Input
													{...field}
													disabled={!isDefaultLanguage}
													placeholder={t("enterTagSlug")}
												/>
											</FormControl>
											<FormDescription>
												{!isDefaultLanguage
													? t("slugEditDisabled")
													: t("slugDescription")}
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="description"
									render={({ field }) => (
										<FormItem>
											<FormLabel>{t("tagDescription")}</FormLabel>
											<FormControl>
												<Textarea
													placeholder={t("enterTagDescription")}
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* SEO设置 */}
							<div className="border-t pt-6">
								<h3 className="text-base font-medium mb-4">
									{t("seoSettings")}
								</h3>
								<div className="space-y-4">
									<FormField
										control={form.control}
										name="metaTitle"
										render={({ field }) => (
											<FormItem>
												<FormLabel>{t("metaTitle")}</FormLabel>
												<FormControl>
													<Input placeholder={t("enterMetaTitle")} {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="metaDescription"
										render={({ field }) => (
											<FormItem>
												<FormLabel>{t("metaDescription")}</FormLabel>
												<FormControl>
													<Textarea
														placeholder={t("enterMetaDescription")}
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>

							<DialogFooter>
								<Button
									type="button"
									variant="outline"
									onClick={handleReset}
									disabled={isSaving}
								>
									{t("reset")}
								</Button>
								<Button type="submit" loading={isSaving}>
									{t("saveTag")}
								</Button>
							</DialogFooter>
						</form>
					</Form>
				)}
			</DialogContent>
		</Dialog>
	)
}
