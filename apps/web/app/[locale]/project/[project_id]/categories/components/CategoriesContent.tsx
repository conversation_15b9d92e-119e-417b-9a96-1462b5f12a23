"use client"

import { TranslationProvider } from "@/lib/components/translation/TranslationContext"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@repo/ui/components"
import { useState, useCallback } from "react"
import ArticleCategories from "./ArticleCategories"
import GameCategories from "./GameCategories"
import {
	Language,
	ProjectLanguage,
	ProjectLocaleSiteSettingType,
} from "@repo/shared-types"
import { fetchPost } from "@repo/utils/react"
interface CategoriesContentProps {
	projectId: string
	languages: Language[]
	defaultLanguage: ProjectLanguage
}

export default function CategoriesContent({
	projectId,
	languages,
	defaultLanguage,
}: CategoriesContentProps) {
	const [activeTab, setActiveTab] = useState<ProjectLocaleSiteSettingType>(
		ProjectLocaleSiteSettingType.GameCategories,
	)

	return (
		<TranslationProvider
			languages={languages}
			defaultLanguage={defaultLanguage}
		>
			<div className="flex-1 overflow-y-auto p-4 pb-20">
				<div className="max-w-6xl mx-auto relative">
					<Tabs
						value={activeTab}
						onValueChange={(value) =>
							setActiveTab(value as ProjectLocaleSiteSettingType)
						}
						className="w-full"
					>
						<TabsList className="mb-6 h-12">
							<TabsTrigger
								value={ProjectLocaleSiteSettingType.GameCategories}
								className="px-10 py-4 text-md"
							>
								游戏分类
							</TabsTrigger>
							<TabsTrigger
								value={ProjectLocaleSiteSettingType.ArticleCategories}
								className="px-10 py-4 text-md"
							>
								文章分类
							</TabsTrigger>
						</TabsList>
						<TabsContent value={ProjectLocaleSiteSettingType.GameCategories}>
							<GameCategories projectId={projectId} />
						</TabsContent>
						<TabsContent value={ProjectLocaleSiteSettingType.ArticleCategories}>
							<ArticleCategories projectId={projectId} />
						</TabsContent>
					</Tabs>
				</div>
			</div>
		</TranslationProvider>
	)
}
