"use client"

import { TranslationProvider } from "@/lib/components/translation/TranslationContext"
import { Language, ProjectLanguage } from "@repo/shared-types"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@repo/ui/components"
import { useState } from "react"
import { useSiteSettings } from "../hooks/useSiteSettings"
import AdvancedSettings from "./AdvancedSettings"
import BasicSettings from "./BasicSettings"
import SEOSettings from "./SEOSettings"

export default function SiteSettingsContent({
	projectId,
	languages,
	defaultLanguage,
}: {
	projectId: string
	languages: Language[]
	defaultLanguage: ProjectLanguage
}) {
	const [activeTab, setActiveTab] = useState("basic")

	const {
		isLoading,
		isSavingBasic,
		isSavingAdvanced,
		siteSettings,
		updateSettings,
		handleSaveBasic,
		handleResetBasic,
		handleSaveAdvanced,
		handleResetAdvanced,
	} = useSiteSettings(projectId)

	return (
		<div className="container mx-auto p-6 mb-8">
			<TranslationProvider
				languages={languages}
				defaultLanguage={defaultLanguage}
			>
				<Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
					<TabsList className="mb-6 h-12">
						<TabsTrigger value="basic" className="px-10 py-4 text-md">
							基础设置
						</TabsTrigger>
						<TabsTrigger value="seo" className="px-10 py-4 text-md">
							SEO 设置
						</TabsTrigger>
						<TabsTrigger value="advanced" className="px-10 py-4 text-md">
							高级设置
						</TabsTrigger>
					</TabsList>

					{isLoading ? (
						<div className="py-8 text-center text-muted-foreground">
							加载中...
						</div>
					) : (
						<>
							<TabsContent value="basic">
								<BasicSettings
									settings={siteSettings}
									updateSettings={updateSettings}
									projectId={projectId}
									isSaving={isSavingBasic}
									onSave={handleSaveBasic}
									onReset={handleResetBasic}
								/>
							</TabsContent>

							<TabsContent value="seo">
								<SEOSettings projectId={projectId} />
							</TabsContent>

							<TabsContent value="advanced">
								<AdvancedSettings
									settings={siteSettings}
									updateSettings={updateSettings}
									projectId={projectId}
									isSaving={isSavingAdvanced}
									onSave={handleSaveAdvanced}
									onReset={handleResetAdvanced}
								/>
							</TabsContent>
						</>
					)}
				</Tabs>
			</TranslationProvider>
		</div>
	)
}
