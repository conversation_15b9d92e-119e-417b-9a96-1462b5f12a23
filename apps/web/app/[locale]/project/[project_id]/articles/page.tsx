import { Metada<PERSON> } from "next"
import { getTranslations, setRequestLocale } from "next-intl/server"
import { alternatesLanguage } from "@repo/i18n"
import { siteConfig } from "@/lib/config/site"
import Articles from "./components/Articles"
import { TranslationProvider } from "@/lib/components/translation/TranslationContext"
import { ProjectLanguage } from "@repo/shared-types"
import { getByProjectId } from "@/lib/services/ProjectSiteSettingsServices"
import { getLanguageName, Language } from "@repo/shared-types"

type Props = {
	params: Promise<{ locale: string; project_id: string }>
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
	const { locale } = await params
	setRequestLocale(locale)
	const t = await getTranslations({ locale: locale })
	return {
		title: `${t("Articles.title")} | ${t("slogan")}`,
		description: t("Articles.description"),
		alternates: {
			languages: alternatesLanguage(""),
		},
		icons: {
			icon: siteConfig.icon,
			apple: siteConfig.appleIcon,
		},
		robots: {
			index: false,
			follow: true,
		},
	}
}

export default async function Page({ params }: Props) {
	const { project_id, locale } = await params
	setRequestLocale(locale)
	const project = await getByProjectId(project_id)

	const defaultLanguage = (project!.defaultLocale ||
		ProjectLanguage.EN) as ProjectLanguage
	const languageList = ((project?.languanges as Array<string>) ?? []).map(
		(code: string) => {
			return { code, name: getLanguageName(code) }
		},
	) as Language[]

	return (
		<div className="container py-6">
			<TranslationProvider
				languages={languageList}
				defaultLanguage={defaultLanguage}
			>
				<Articles projectId={project_id} />
			</TranslationProvider>
		</div>
	)
}
