"use client"

import { useCallback, useState } from "react"
import { toast } from "sonner"

import { useTranslationContext } from "@/lib/components/translation/TranslationContext"
import { ProjectGameLocaleType, ProjectLanguage } from "@repo/shared-types"
import { fetchGet, fetchPost } from "@repo/utils/react"
import useSWR from "swr"
import useSWRMutation from "swr/mutation"
import { GameDetailContent } from "@repo/shared-types"

export interface ContentModule extends GameDetailContent {
	operation?: "create" | "update" | "delete"
	id?: string
}

export interface ContentTabData {
	modules: ContentModule[]
}

export function useGameContentsForm(projectId: string, gameId: string) {
	const { currentLanguage } = useTranslationContext()
	const [isSaving, setIsSaving] = useState(false)
	// 获取游戏内容数据
	const {
		data,
		error,
		isLoading,
		mutate: refreshData,
	} = useSWR<ContentTabData>(
		`/api/project-games/content/list?projectId=${projectId}&gameId=${gameId}
		&locale=${currentLanguage}&type=${ProjectGameLocaleType.Content}`,
		async (url) => {
			const res = await fetchGet(url)
			const contentModules = res ?? []

			// 如果没有找到任何模块，返回空数组
			if (contentModules.length === 0) {
				return { modules: [] }
			}

			// 将每个记录转换为模块，并默认设置为update操作
			const result = {
				modules: contentModules.map((item: any) => ({
					id: item.id,
					tabId: item.content.tabId,
					type: item.content.type,
					title: item.content.title,
					text: item.content.text,
					jsonContent: item.content.jsonContent,
					icon: item.content.icon,
					operation: "update" as const,
				})),
			}
			return result
		},
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: false,
			onError: (err) => {
				console.error("获取游戏内容失败:", err)
				toast.error("获取游戏内容失败")
			},
		},
	)

	const {
		data: localeData,
		isMutating: isLocaleLoading,
		trigger,
	} = useSWRMutation<ContentModule>(
		`/api/project-games/content`,
		(url: string, { arg }: { arg: { id: string; locale: ProjectLanguage } }) =>
			fetchGet(`${url}?locale=${arg.locale}&id=${arg.id}`).then((res) => {
				return res
					? {
							id: res.id,
							tabId: res.content.tabId,
							...res.content,
						}
					: null
			}),
	)

	// 保存游戏内容
	const saveOrUpdate = useCallback(
		async (editData: ContentTabData) => {
			try {
				// 使用新的批量API格式，一次性发送所有模块数据
				const requestData = {
					projectId,
					gameId,
					locale: currentLanguage,
					type: ProjectGameLocaleType.Content,
					contents: editData.modules.map((module) => {
						const contentData = {
							// 对于新创建的模块，不传递id字段
							...(module.operation !== "create" &&
								module.id && { id: module.id }),
							// 确保所有模块都有 tabId 属性
							tabId: module.tabId,
							type: module.type,
							title: module.title,
							text: module.text,
							jsonContent: module.jsonContent,
							icon: module.icon,
							operation: module.operation || "update",
						}

						return contentData
					}),
				}

				const response = await fetchPost(
					"/api/project-games/content/list",
					requestData,
				)

				// 刷新数据
				refreshData()

				return response
			} catch (error) {
				console.error("保存游戏内容失败:", error)
				throw error
			}
		},
		[gameId, projectId, currentLanguage, refreshData],
	)

	// 保存游戏内容
	const handleSave = async (values: ContentTabData) => {
		if (!values) {
			return
		}

		setIsSaving(true)
		try {
			const result = await saveOrUpdate(values)
			return result
		} catch (error) {
			console.error("保存失败:", error)
			throw error
		} finally {
			setIsSaving(false)
		}
	}

	// 更新内容数据
	const updateContentData = useCallback(
		(newData: ContentTabData) => {
			// 直接更新本地数据，不发送API请求
			refreshData(newData, false)
		},
		[data, refreshData],
	)

	return {
		isLoading: isLoading || isSaving || isLocaleLoading,
		data,
		localeData,
		locale: currentLanguage,
		isLocaleLoading,
		trigger,
		error,
		handleSave,
		refreshFormData: refreshData,
		updateContentData,
	}
}
