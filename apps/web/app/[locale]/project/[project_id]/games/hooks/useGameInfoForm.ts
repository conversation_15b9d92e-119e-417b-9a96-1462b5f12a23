"use client"

import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { useState, useEffect } from "react"
import { toast } from "sonner"
import { GameInfoSettings } from "@repo/shared-types"
import { fetchGet, fetchPut } from "@repo/utils/react"
import useSWR from "swr"

// 定义表单验证模式
export const gameInfoFormSchema = z.object({
	developer: z.string().optional(),
	rating: z.number().min(0).max(5).optional(),
	releaseDate: z.string().optional(),
	technology: z.string().optional(),
	platform: z.string().optional(),
	ageRating: z.string().optional(),
	localization: z.string().optional(),
	screenOrientation: z.string().optional(),
	cloudSaves: z.string().optional(),
	authorizationSupport: z.string().optional(),
})

export type GameInfoFormData = z.infer<typeof gameInfoFormSchema>

interface UseGameInfoFormProps {
	projectId: string
	gameId: string
}

export function useGameInfoForm({ projectId, gameId }: UseGameInfoFormProps) {
	const [isSaving, setIsSaving] = useState(false)
	const [error, setError] = useState<string | null>(null)

	// 使用 SWR 获取游戏数据
	const {
		data: gameData,
		isLoading,
		mutate: refreshGameData,
	} = useSWR<any>(
		gameId && gameId !== "new-game"
			? `/api/project-games?id=${gameId}`
			: null,
		fetchGet,
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: false,
		},
	)

	// 设置表单
	const form = useForm<GameInfoFormData>({
		resolver: zodResolver(gameInfoFormSchema),
		defaultValues: {
			developer: "",
			rating: 4.5,
			releaseDate: "",
			technology: "",
			platform: "",
			ageRating: "",
			localization: "",
			screenOrientation: "",
			cloudSaves: "",
			authorizationSupport: "",
		},
		mode: "onChange",
		criteriaMode: "all",
	})

	// 当游戏数据加载完成后更新表单
	useEffect(() => {
		if (gameData?.settings) {
			const settings = gameData.settings as GameInfoSettings
			form.reset({
				developer: settings.developer || "",
				rating: settings.rating || 4.5,
				releaseDate: settings.releaseDate || "",
				technology: settings.technology || "",
				platform: settings.platform || "",
				ageRating: settings.ageRating || "",
				localization: settings.localization || "",
				screenOrientation: settings.screenOrientation || "",
				cloudSaves: settings.cloudSaves || "",
				authorizationSupport: settings.authorizationSupport || "",
			})
		}
	}, [gameData, form])

	// 提交表单
	const handleSubmit = async (data: GameInfoFormData) => {
		if (gameId === "new-game") {
			toast.error("请先保存游戏基本信息")
			return
		}

		setIsSaving(true)
		setError(null)

		try {
			// 更新游戏的 settings 字段
			await fetchPut(`/api/project-games/update-game`, {
				projectId,
				gameId,
				settings: data,
			})

			toast.success("游戏信息已保存", {
				position: "top-center",
				duration: 3000,
			})

			// 刷新数据
			refreshGameData()
		} catch (error) {
			console.error("保存游戏信息失败:", error)
			const errorMessage = error instanceof Error ? error.message : "保存失败"
			setError(errorMessage)
			toast.error(errorMessage, {
				position: "top-center",
				duration: 3000,
			})
		} finally {
			setIsSaving(false)
		}
	}

	return {
		form,
		isLoading,
		isSaving,
		error,
		handleSubmit,
		gameInfo: gameData?.settings as GameInfoSettings,
		refreshGameData,
	}
}
