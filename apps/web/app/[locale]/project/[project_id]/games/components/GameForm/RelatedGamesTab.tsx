"use client"

import { useTranslationContext } from "@/lib/components/translation/TranslationContext"
import { ProjectLanguage } from "@repo/shared-types"
import {
  Alert,
  AlertDescription,
  AlertTitle,
  Button,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Switch,
} from "@repo/ui/components"
import { AlertCircle } from "lucide-react"
import { useTranslations } from "next-intl"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { useGameRelatedGamesForm } from "../../hooks/useGameRelatedGamesForm"
import GameSelector from "./GameSelector"
import RelatedGameList from "./RelatedGameList"

interface RelatedGamesTabProps {
	projectId: string
	gameId: string
	defaultLanguage?: ProjectLanguage
	languages?: { code: string; name: string }[]
}

export default function RelatedGamesTab({
	projectId,
	gameId,
	defaultLanguage,
	languages,
}: RelatedGamesTabProps) {
	const t = useTranslations("Games")
	const { currentLanguage } = useTranslationContext()

	// 使用自定义钩子获取和管理相关游戏数据
	const {
		formData,
		updateFormData,
		updateRelatedGamesData,
		isLoading,
		isSaving,
	} = useGameRelatedGamesForm({ projectId, gameId })

	// 关联类型
	const [relationType, setRelationType] = useState<
		"similar" | "series" | "recommended"
	>("similar")

	// 创建表单
	const form = useForm({
		defaultValues: {
			mode: formData.relatedGamesConfig.mode,
			sameCategory: formData.relatedGamesConfig.rules.sameCategory,
			sameTags: formData.relatedGamesConfig.rules.sameTags,
			maxAutoItems: formData.relatedGamesConfig.rules.maxAutoItems,
		},
	})

	// 处理模式切换
	const handleModeChange = (checked: boolean) => {
		const newMode = checked ? "auto" : "manual"

		updateFormData({
			relatedGamesConfig: {
				...formData.relatedGamesConfig,
				mode: newMode,
			},
		})

		form.setValue("mode", newMode)
	}

	// 处理游戏选择
	const handleGameSelect = (game: any) => {
		const newGame = {
			id: game.id,
			name: game.name,
			relationType: relationType,
			order: formData.relatedGames.length + 1,
			isAuto: false,
		}

		const newRelatedGames = [...formData.relatedGames, newGame]

		updateFormData({
			relatedGames: newRelatedGames,
		})
	}

	// 处理游戏移除
	const handleGameRemove = (gameId: string) => {
		const newRelatedGames = formData.relatedGames.filter(
			(game) => game.id !== gameId,
		)

		updateFormData({
			relatedGames: newRelatedGames,
		})
	}

	// 处理游戏排序
	const handleGameReorder = (reorderedGames: any[]) => {
		updateFormData({
			relatedGames: reorderedGames,
		})
	}

	// 处理关联类型变更
	const handleRelationTypeChange = (
		type: "similar" | "series" | "recommended",
	) => {
		setRelationType(type)
	}

	// 处理自动规则变更
	const handleAutoRuleChange = (rule: string, value: any) => {
		updateFormData({
			relatedGamesConfig: {
				...formData.relatedGamesConfig,
				rules: {
					...formData.relatedGamesConfig.rules,
					[rule]: value,
				},
			},
		})

		form.setValue(rule as any, value)
	}

	// 处理表单提交
	const handleSubmit = async () => {
		await updateRelatedGamesData()
	}

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(handleSubmit)}>
				<Card>
					<CardHeader>
						<CardTitle>{t("relatedGamesSettings")}</CardTitle>
						<CardDescription>{t("relatedGamesDescription")}</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-6">
							<div className="flex items-center justify-between bg-muted/50 p-4 rounded-lg">
								<div className="space-y-0.5">
									<Label htmlFor="recommend-mode">{t("recommendMode")}</Label>
									<p className="text-sm text-muted-foreground">
										{t("recommendModeDescription")}
									</p>
								</div>
								<div className="flex items-center space-x-2">
									<span
										className={`text-sm ${formData.relatedGamesConfig.mode === "manual" ? "text-primary font-medium" : "text-muted-foreground"}`}
									>
										{t("manual")}
									</span>
									<Switch
										id="recommend-mode"
										checked={formData.relatedGamesConfig.mode === "auto"}
										onCheckedChange={handleModeChange}
									/>
									<span
										className={`text-sm ${formData.relatedGamesConfig.mode === "auto" ? "text-primary font-medium" : "text-muted-foreground"}`}
									>
										{t("auto")}
									</span>
								</div>
							</div>

							{/* 自动模式内容 */}
							{formData.relatedGamesConfig.mode === "auto" && (
								<div className="space-y-4 mt-4">
									<Alert>
										<AlertCircle className="h-4 w-4" />
										<AlertTitle>{t("autoRecommendationNote")}</AlertTitle>
										<AlertDescription>
											{t("autoRecommendationDescription")}
										</AlertDescription>
									</Alert>

									<div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
										<div className="space-y-6">
											<FormField
												control={form.control}
												name="sameCategory"
												render={() => (
													<FormItem className="flex items-center justify-between space-y-0">
														<FormLabel>{t("sameCategoryRule")}</FormLabel>
														<FormControl>
															<Switch
																checked={
																	formData.relatedGamesConfig.rules.sameCategory
																}
																onCheckedChange={(checked) =>
																	handleAutoRuleChange("sameCategory", checked)
																}
															/>
														</FormControl>
													</FormItem>
												)}
											/>

											<FormField
												control={form.control}
												name="sameTags"
												render={() => (
													<FormItem className="flex items-center justify-between space-y-0">
														<FormLabel>{t("sameTagsRule")}</FormLabel>
														<FormControl>
															<Switch
																checked={
																	formData.relatedGamesConfig.rules.sameTags
																}
																onCheckedChange={(checked) =>
																	handleAutoRuleChange("sameTags", checked)
																}
															/>
														</FormControl>
													</FormItem>
												)}
											/>
										</div>

										<div>
											<FormField
												control={form.control}
												name="maxAutoItems"
												render={() => (
													<FormItem>
														<FormLabel>{t("maxAutoItems")}</FormLabel>
														<Select
															value={String(
																formData.relatedGamesConfig.rules.maxAutoItems,
															)}
															onValueChange={(value) =>
																handleAutoRuleChange(
																	"maxAutoItems",
																	Number.parseInt(value),
																)
															}
														>
															<FormControl>
																<SelectTrigger>
																	<SelectValue
																		placeholder={t("selectMaxItems")}
																	/>
																</SelectTrigger>
															</FormControl>
															<SelectContent>
																{[1, 2, 3, 4, 5, 6, 8, 10].map((num) => (
																	<SelectItem key={num} value={String(num)}>
																		{num}
																	</SelectItem>
																))}
															</SelectContent>
														</Select>
														<FormDescription>
															{t("maxAutoItemsDescription")}
														</FormDescription>
													</FormItem>
												)}
											/>
										</div>
									</div>
								</div>
							)}

							{/* 手动模式内容 */}
							{formData.relatedGamesConfig.mode === "manual" && (
								<div className="space-y-4 mt-4">
									<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
										<div>
											<h3 className="text-lg font-medium mb-4">
												{t("selectGames")}
											</h3>
											<GameSelector
												projectId={projectId}
												currentGameId={gameId}
												onSelect={handleGameSelect}
												relationType={relationType}
												onRelationTypeChange={handleRelationTypeChange}
											/>
										</div>
										<div>
											<h3 className="text-lg font-medium mb-4">
												{t("selectedGames")}
											</h3>
											<RelatedGameList
												games={formData.relatedGames}
												onRemove={handleGameRemove}
												onReorder={handleGameReorder}
											/>
										</div>
									</div>
								</div>
							)}
						</div>
						<div className="fixed bottom-0 left-0 right-0 bg-background/80 backdrop-blur-sm border-t border-border py-3 sm:py-4 px-4 sm:px-6 z-10 shadow-lg">
							<div className="max-w-6xl mx-auto flex justify-end w-full">
								<Button
									type="reset"
									variant="outline"
									disabled={isSaving}
									className="mr-3"
								>
									{t("reset")}
								</Button>
								<Button type="submit" loading={isSaving}>
									{isSaving ? t("saving") : t("saveSettings")}
								</Button>
							</div>
						</div>
					</CardContent>
				</Card>
			</form>
		</Form>
	)
}
