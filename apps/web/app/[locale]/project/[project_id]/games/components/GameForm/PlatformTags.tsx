"use client"

import { Badge, Input } from "@repo/ui/components"
import { X } from "lucide-react"
import { useState, KeyboardEvent } from "react"
import { cn } from "@repo/ui/utils"

interface PlatformTagsProps {
	value: string
	onChange: (value: string) => void
	placeholder?: string
	className?: string
}

export function PlatformTags({
	value,
	onChange,
	placeholder = "输入平台名称，按回车添加",
	className,
}: PlatformTagsProps) {
	const [inputValue, setInputValue] = useState("")

	// 将字符串转换为数组
	const platforms = value
		? value
				.split(",")
				.map((p) => p.trim())
				.filter(Boolean)
		: []

	const addPlatform = (platform: string) => {
		const trimmedPlatform = platform.trim()
		if (trimmedPlatform && !platforms.includes(trimmedPlatform)) {
			const newPlatforms = [...platforms, trimmedPlatform]
			onChange(newPlatforms.join(", "))
		}
		setInputValue("")
	}

	const removePlatform = (platformToRemove: string) => {
		const newPlatforms = platforms.filter((p) => p !== platformToRemove)
		onChange(newPlatforms.join(", "))
	}

	const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter") {
			e.preventDefault()
			addPlatform(inputValue)
		} else if (e.key === "Backspace" && !inputValue && platforms.length > 0) {
			const lastPlatform = platforms[platforms.length - 1]
			if (lastPlatform) {
				removePlatform(lastPlatform)
			}
		}
	}

	return (
		<div className={cn("space-y-2", className)}>
			<div className="flex flex-wrap gap-2 min-h-[2rem] p-2 border rounded-md bg-background">
				{platforms.map((platform, index) => (
					<Badge
						key={index}
						variant="secondary"
						className="flex items-center gap-1 px-2 py-1"
					>
						{platform}
						<X
							className="h-3 w-3 cursor-pointer hover:text-destructive"
							onClick={() => removePlatform(platform)}
						/>
					</Badge>
				))}
				<Input
					value={inputValue}
					onChange={(e) => setInputValue(e.target.value)}
					onKeyDown={handleKeyDown}
					placeholder={platforms.length === 0 ? placeholder : ""}
					className="border-none shadow-none focus-visible:ring-0 flex-1 min-w-[120px] h-auto p-0"
				/>
			</div>
		</div>
	)
}
