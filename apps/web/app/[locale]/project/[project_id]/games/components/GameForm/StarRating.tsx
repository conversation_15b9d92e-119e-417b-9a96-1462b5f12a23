"use client"

import { Star } from "lucide-react"
import { cn } from "@repo/ui/utils"

interface StarRatingProps {
	value: number
	onChange?: (value: number) => void
	readonly?: boolean
	size?: "sm" | "md" | "lg"
	className?: string
}

export function StarRating({ 
	value, 
	onChange, 
	readonly = false, 
	size = "md",
	className 
}: StarRatingProps) {
	const sizeClasses = {
		sm: "h-3 w-3",
		md: "h-4 w-4", 
		lg: "h-5 w-5"
	}

	const handleStarClick = (rating: number) => {
		if (!readonly && onChange) {
			onChange(rating)
		}
	}

	return (
		<div className={cn("flex items-center gap-1", className)}>
			{[1, 2, 3, 4, 5].map((star) => (
				<Star
					key={star}
					className={cn(
						sizeClasses[size],
						star <= value
							? "fill-yellow-400 text-yellow-400"
							: "text-muted-foreground",
						!readonly && "cursor-pointer hover:text-yellow-300"
					)}
					onClick={() => handleStarClick(star)}
				/>
			))}
			{!readonly && (
				<span className="ml-2 text-sm text-muted-foreground">
					{value.toFixed(1)} / 5.0
				</span>
			)}
		</div>
	)
}
