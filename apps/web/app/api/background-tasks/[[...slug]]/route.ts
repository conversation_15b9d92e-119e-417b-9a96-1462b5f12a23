import { NextRequest } from "next/server"
import { hasProjectAccess, authUser } from "@repo/auth/server"
import { prisma } from "@repo/db"
import { R } from "@repo/utils/server"
import { AutoRouter } from "itty-router"
import { getLogger } from "@repo/logger"
import { requeueTask } from "@/lib/services/BackgroundTaskService"
import { BACKGROUND_TASK_STATUS } from "@repo/shared-types"

const logger = getLogger("BackgroundTasks")

// 创建路由处理器
const router = AutoRouter({ base: "/api/background-tasks" })

// 获取任务列表
router.get("/", async (request) => {
  try {
    const projectId = request.query.projectId as string
    const page = parseInt(request.query.page as string) || 1
    const limit = parseInt(request.query.limit as string) || 20
    const status = request.query.status as string
    const type = request.query.type as string

    if (!projectId) {
      return R.error("项目ID不能为空")
    }

    // 验证项目访问权限
    await hasProjectAccess(projectId)

    // 构建查询条件
    const where: any = { projectId }
    if (status) where.status = status
    if (type) where.type = type

    const skip = (page - 1) * limit

    // 查询任务列表和总数
    const [tasks, total] = await Promise.all([
      prisma.backgroundTask.findMany({
        where,
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      }),
      prisma.backgroundTask.count({ where })
    ])

    return R.ok({
      data: tasks,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    logger.error("获取任务列表失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

// 获取单个任务详情
router.get("/:taskId", async (request) => {
  try {
    const taskId = request.params.taskId as string

    if (!taskId) {
      return R.error("任务ID不能为空")
    }

    const task = await prisma.backgroundTask.findUnique({
      where: { id: taskId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        project: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    if (!task) {
      return R.error("任务不存在")
    }

    // 验证项目访问权限
    await hasProjectAccess(task.projectId)

    return R.ok(task)
  } catch (error) {
    logger.error("获取任务详情失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

// 重试任务
router.post("/:taskId/retry", async (request) => {
  try {
    const taskId = request.params.taskId as string

    if (!taskId) {
      return R.error("任务ID不能为空")
    }

    // 验证用户身份
    const user = await authUser()
    if (!user) {
      return R.error("用户未登录", { status: 401 })
    }

    const task = await prisma.backgroundTask.findUnique({
      where: { id: taskId }
    })

    if (!task) {
      return R.error("任务不存在")
    }

    // 验证项目访问权限
    await hasProjectAccess(task.projectId)

    // 只有失败的任务才能重试
    if (task.status !== BACKGROUND_TASK_STATUS.FAILED) {
      return R.error("只有失败的任务才能重试")
    }

    // 重新发送任务到队列
    await requeueTask(taskId)

    return R.ok({ message: "任务已重新加入队列" })
  } catch (error) {
    logger.error("重试任务失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

// 删除任务
router.delete("/:taskId", async (request) => {
  try {
    const taskId = request.params.taskId as string

    if (!taskId) {
      return R.error("任务ID不能为空")
    }

    // 验证用户身份
    const user = await authUser()
    if (!user) {
      return R.error("用户未登录", { status: 401 })
    }

    const task = await prisma.backgroundTask.findUnique({
      where: { id: taskId }
    })

    if (!task) {
      return R.error("任务不存在")
    }

    // 验证项目访问权限
    await hasProjectAccess(task.projectId)

    // 只有已完成或失败的任务才能删除
    if (![BACKGROUND_TASK_STATUS.SUCCESS, BACKGROUND_TASK_STATUS.FAILED].includes(task.status as any)) {
      return R.error("只有已完成或失败的任务才能删除")
    }

    await prisma.backgroundTask.delete({
      where: { id: taskId }
    })

    return R.ok({ message: "任务已删除" })
  } catch (error) {
    logger.error("删除任务失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

// 批量删除任务
router.post("/batch-delete", async (request) => {
  try {
    const body = await request.json()
    const { taskIds, projectId } = body

    if (!taskIds || !Array.isArray(taskIds) || taskIds.length === 0) {
      return R.error("任务ID列表不能为空")
    }

    if (!projectId) {
      return R.error("项目ID不能为空")
    }

    // 验证用户身份
    const user = await authUser()
    if (!user) {
      return R.error("用户未登录", { status: 401 })
    }

    // 验证项目访问权限
    await hasProjectAccess(projectId)

    // 只删除已完成或失败的任务
    const deletedCount = await prisma.backgroundTask.deleteMany({
      where: {
        id: { in: taskIds },
        projectId,
        status: { in: [BACKGROUND_TASK_STATUS.SUCCESS, BACKGROUND_TASK_STATUS.FAILED] }
      }
    })

    return R.ok({ 
      message: `已删除 ${deletedCount.count} 个任务`,
      deletedCount: deletedCount.count
    })
  } catch (error) {
    logger.error("批量删除任务失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

// 获取任务统计信息
router.get("/stats/:projectId", async (request) => {
  try {
    const projectId = request.params.projectId as string

    if (!projectId) {
      return R.error("项目ID不能为空")
    }

    // 验证项目访问权限
    await hasProjectAccess(projectId)

    // 统计各状态的任务数量
    const stats = await prisma.backgroundTask.groupBy({
      by: ['status'],
      where: { projectId },
      _count: {
        id: true
      }
    })

    const result = {
      total: 0,
      pending: 0,
      processing: 0,
      success: 0,
      failed: 0,
      failedQueued: 0
    }

    stats.forEach(stat => {
      result.total += stat._count.id
      switch (stat.status) {
        case BACKGROUND_TASK_STATUS.PENDING:
          result.pending = stat._count.id
          break
        case BACKGROUND_TASK_STATUS.PROCESSING:
          result.processing = stat._count.id
          break
        case BACKGROUND_TASK_STATUS.SUCCESS:
          result.success = stat._count.id
          break
        case BACKGROUND_TASK_STATUS.FAILED:
          result.failed = stat._count.id
          break
        case BACKGROUND_TASK_STATUS.FAILED_QUEUED:
          result.failedQueued = stat._count.id
          break
      }
    })

    return R.ok(result)
  } catch (error) {
    logger.error("获取任务统计失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

export const GET = async (request: NextRequest) => router.fetch(request)
export const POST = async (request: NextRequest) => router.fetch(request)
export const DELETE = async (request: NextRequest) => router.fetch(request)
