import { z } from "zod"
import { R, getProjectIdFromSHA256 } from "@repo/utils/server"
import { router, queryParamsSchema } from "."
import { prisma } from "@repo/db"
import type { ProjectGameLocale } from "@repo/db"
import {
	GameLocaleContent,
	ProjectGame,
	GameInfoData,
	GameType,
	ProjectGameLocaleType,
	GameLocaleArray,
	GameDownloadSettings,
	GameBackgroundSettings,
	MetadataInfo,
	TabContent,
	BreadcrumbItem,
	GameDetailContent,
	GameVideo,
	CommentsConfig,
	ProjectGameStatus,
	GameTag,
	GameDetailContentType,
} from "@repo/shared-types"

// 获取项目下所有游戏（包括多语言内容）
router.get("/games", async (request) => {
	try {
		const projectId = request.projectId as string

		// 查询项目下所有游戏
		const games = await prisma.projectGame.findMany({
			include: {
				gameLocales: true,
			},
			where: {
				projectId,
				status: ProjectGameStatus.Completed,
			},
			orderBy: { createdAt: "desc" },
		})
		// 获取项目基本设置
		const projectSiteSetting = await prisma.projectSiteSetting.findFirst({
			where: { projectId },
		})
		if (!projectSiteSetting) return R.error("项目设置不存在")

		// 获取支持的语言列表
		const locales = projectSiteSetting.languanges as unknown as string[]

		// 查询所有游戏的多语言内容
		const projectGames = await Promise.all(
			games.map(async (game) => {
				const gameInfo: GameInfoData = {
					settings: game.settings as Record<string, any>,
					id: game.id,
					gameUrl: game.gameIframeUrl || "",
					// 游戏下载配置
					gameDownload:
						game.gameDownloadSettings as unknown as GameDownloadSettings,
					// 游戏区域的背景配置
					background:
						game.backgroundSettings as unknown as GameBackgroundSettings,
				}

				// 查询游戏tag对应的tag详情(包含多语言)
				const gameTagLocales = await prisma.gameTagLocale.findMany({
					where: {
						tagId: { in: game.tags },
					},
					include: {
						tag: true,
					},
				})
				// 获取指定类别游戏多语言 content字段内容（根据语言和类型）(返回多个)
				const findGameContentLocales = (
					locale: string,
					type: ProjectGameLocaleType,
				) => {
					const gameLocales = game.gameLocales || []
					return (
						gameLocales.filter(
							(gameLocale: ProjectGameLocale) =>
								gameLocale.locale === locale && gameLocale.type === type,
						) || []
					).map((gameLocale: ProjectGameLocale) => {
						return gameLocale.content as unknown as GameDetailContent
					})
				}

				const getGameLocale = (locale: string, type: ProjectGameLocaleType) => {
					const gameLocales = findGameContentLocales(locale, type) || []
					return gameLocales.length > 0 ? gameLocales[0] : {}
				}

				// 处理游戏相关数据
				let relatedGames = game.relatedGames
					? (game.relatedGames as string[])
					: []
				// 如果relatedGames为空，则根据当前游戏的分类从games当中查找相同分类的游戏Id
				if (relatedGames.length === 0) {
					relatedGames = games
						.filter((game1) => {
							return game1.categories.some((category) => {
								return game1.categories.includes(category)
							})
						})
						.filter((game1) => {
							return game1.id !== game.id
						})
						.map((game1) => {
							return game1.id
						})
					// 取前8个
					relatedGames.splice(8)
				}

				// 相关视频
				const relatedVideos = game.relatedVideos
					? Array.isArray(game.relatedVideos)
						? (game.relatedVideos as any[]).map(
								(video: any, index: number) => video as GameVideo,
							)
						: []
					: []

				// 相关文章列表
				const relatedArticles = game.relatedArticles
					? (game.relatedArticles as string[])
					: []

				let commentsConfig = game.commentsConfig
					? (game.commentsConfig as unknown as CommentsConfig)
					: {
							showComments: false,
							commentsSystemConfig: {
								type: "none",
								config: {},
							},
						}
				try {
					const defaultLocale = game.gameLocales.find(
						(lang) =>
							lang.locale === projectSiteSetting.defaultLocale &&
							lang.type === ProjectGameLocaleType.Content &&
							(lang?.content as any)?.tabId === GameDetailContentType.Comments,
					)
					console.log("defaultLocale", defaultLocale)
					if (defaultLocale) {
						commentsConfig = {
							...commentsConfig,
							history: [
								...((defaultLocale.content as any)?.jsonContent.comments || []),
							],
						} as CommentsConfig
					}
				} catch (error) {
					console.error("获取评论历史失败:", error)
				}

				// 如果是主页游戏slug要改为 空
				if (game.isPrimary) {
					game.slug = ""
				}

				const localeContents: GameLocaleArray[] = locales.map(
					(locale: string) => {
						// 一个语言只会有一个Metadata数据
						const metadata =
							(getGameLocale(
								locale,
								ProjectGameLocaleType.Metadata,
							) as unknown as MetadataInfo) || {}
						// 一个语言下有多个Content数据
						const localeDataContent =
							findGameContentLocales(locale, ProjectGameLocaleType.Content) ||
							[]
						// 在localeDataContent第一个位置插入推荐游戏
						localeDataContent.unshift({
							tabId: "relatedGames",
							// 模板端会根据type来匹配对应的国际化内容
							title: "",
							// 模板端会根据type来匹配对应的图标
							icon: "",
							type: GameDetailContentType.RelatedGames,
							// 推荐游戏ID集合
							jsonContent: relatedGames,
						})
						// 如果相关视频不为空，则加入到localeDataContent最后
						if (relatedVideos.length > 0) {
							localeDataContent.push({
								tabId: "relatedVideos",
								// 模板端会根据type来匹配对应的国际化内容
								title: "",
								// 模板端会根据type来匹配对应的图标
								icon: "",
								type: GameDetailContentType.RelatedVideos,
								// 相关视频
								jsonContent: relatedVideos,
							})
						}
						//构建面包屑导航
						const breadcrumbItems: BreadcrumbItem[] = [
							{ label: metadata.name || game.name, href: game.slug },
						]
						// 查询游戏tag对应的tag详情(包含多语言)
						const gameTags: GameTag[] = gameTagLocales
							.filter((tagLocale) => tagLocale.locale === locale)
							.map((tagLocale) => {
								return {
									id: tagLocale.tagId,
									name: tagLocale.name,
									locale: tagLocale.locale,
									slug: tagLocale.tag.slug,
									description: tagLocale.description || "",
									imageUrl: tagLocale.imageUrl || "",
									iconName: tagLocale.iconName || "",
									metaTitle: tagLocale.metaTitle || "",
									metaDescription: tagLocale.metaDescription || "",
								}
							})
						return {
							locale: locale,
							content: {
								// 游戏ID（冗余，便于其他接口使用）
								id: game.id,
								// 游戏访问路径（冗余，便于其他接口使用）
								slug: game.slug,
								// 游戏图片（冗余内容）
								gameImages: [game.screenshotUrl || ""],
								// 游戏信息(冗余内容，便于其他接口使用)
								gameInfo: gameInfo,
								// 游戏标签(冗余内容，便于其他接口使用)
								gameTags: gameTags,
								// 游戏名称(国际化后的名称)
								gameName: metadata.name || game.name,
								// 游戏宣传语(国际化后的宣传语)
								gameSlogan: metadata.slogan || "",
								// 游戏描述(国际化后的描述)
								gameDescription: metadata.description || "",
								// 游戏元数据(SEO相关信息)
								metadata: metadata as unknown as MetadataInfo,
								// 面包屑(用于面包屑导航，不需要包含首页)
								breadcrumbItems: breadcrumbItems || [],
								// 游戏详情内容(根据type区分)
								contents:
									(localeDataContent as unknown as GameDetailContent[]) || [],
							} as GameLocaleContent,
						}
					},
				)

				// 构建符合 ProjectGame 接口的游戏详情对象
				return {
					// 游戏ID
					id: game.id,
					// 游戏更新时间(冗余内容，便于其他接口和页面使用)
					updateTime: game.updatedAt,
					// 游戏访问路径
					slug: game.slug,
					// 游戏名称（未国际化的名称）
					name: game.name,
					// 是否是主页游戏
					isPrimary: game.isPrimary || false,
					// 游戏信息
					gameInfo: gameInfo,
					// 游戏类型 iframe|download|popup|placeholder
					gameType: game.gameType as GameType,
					// 游戏标签（标签Id集合，根据标签Id关联标签详情）
					tags: game.tags || [],
					// 游戏分类（分类Id集合，根据分类Id关联分类详情）
					categories: game.categories || [],
					// 游戏图片（第一个为静态图用于列表展示、第二个可以是视频截图或者gif）
					gameImages: [game.screenshotUrl || ""],
					// 游戏视频列表
					relatedVideos: relatedVideos,
					// 相关文章列表(文章ID集合，根据ID关联文章详情)
					relatedArticles: relatedArticles,
					// 相关游戏列表(游戏ID集合，根据ID关联游戏详情)
					relatedGames: relatedGames,
					// 评论配置
					commentsConfig: commentsConfig,
					// 多语言游戏详情内容(每个语言一个对象)
					gameLocales: localeContents,
				} as ProjectGame
			}),
		)

		return R.ok(projectGames)
	} catch (error) {
		console.error("获取项目游戏列表失败:", error)
		return R.error("获取项目游戏列表失败")
	}
})
