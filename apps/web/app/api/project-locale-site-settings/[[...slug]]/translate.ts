import { prisma } from "@repo/db"
import { getLogger } from "@repo/logger"

import { z } from "zod"
import { mergeArray } from "@repo/utils"

const logger = getLogger("ProjectLocaleSiteSettingsTranslate")

/**
 * 翻译项目站点设置
 *
 * @param projectId 项目ID
 * @param sourceLocale 源语言
 * @param targetLocales 目标语言数组
 * @param type 设置类型
 * @returns 翻译结果
 */
export async function translateProjectLocaleSetting(
	projectId: string,
	sourceLocale: string,
	targetLocales: string[],
	type: string,
) {
	try {
		// 获取源语言设置数据
		const sourceSettingData = await prisma.projectLocaleSiteSetting.findFirst({
			where: {
				projectId,
				locale: sourceLocale,
				type,
			},
		})

		if (!sourceSettingData || !sourceSettingData.content) {
			throw new Error(`源语言(${sourceLocale})设置数据不存在`)
		}

		// 存储翻译结果和错误信息
		const results = {
			success: [] as string[],
			failed: [] as { locale: string; error: string }[],
		}

		// 根据设置类型选择适当的Schema
		const contentSchema = getSchemaByType(type)

		// 标记源语言设置为处理中
		await prisma.projectLocaleSiteSetting.update({
			data: {
				status: "PENDING",
				updatedAt: new Date(),
			},
			where: {
				id: sourceSettingData.id,
			},
		})

		// 并行处理所有目标语言的翻译
		await Promise.all(
			targetLocales.map(async (targetLocale) => {
				try {
					// 标记为处理中 - 使用upsert和唯一索引
					await prisma.projectLocaleSiteSetting.upsert({
						where: {
							project_locale_site_setting_unique: {
								projectId,
								locale: targetLocale,
								type,
							},
						},
						update: {
							status: "PENDING",
							updatedAt: new Date(),
						},
						create: {
							projectId,
							locale: targetLocale,
							type,
							content: sourceSettingData.content || {},
							status: "PENDING",
						},
					})

					const sourceContent = sourceSettingData.content
					const isArray = Array.isArray(sourceContent)
					const content = isArray
						? sourceContent.map((value) => contentSchema.parse(value))
						: contentSchema.parse(sourceContent)

					// const translatedContent = await translateObject(
					// 	content,
					// 	contentSchema,
					// 	{
					// 		sourceLanguage: sourceLocale,
					// 		targetLanguage: targetLocale,
					// 		translateKeys: false,
					// 		timeout: 60000,
					// 		isArray,
					// 	},
					// )
					const translatedContent = null

					// 保存翻译后的内容 - 使用事务确保数据一致性
					await prisma.$transaction(async (tx) => {
						await tx.projectLocaleSiteSetting.update({
							where: {
								project_locale_site_setting_unique: {
									projectId,
									locale: sourceLocale,
									type,
								},
							},
							data: {
								status: "COMPLETED",
								updatedAt: new Date(),
							},
						})

						await tx.projectLocaleSiteSetting.update({
							where: {
								project_locale_site_setting_unique: {
									projectId,
									locale: targetLocale,
									type,
								},
							},
							data: {
								content: mergeContent(type, sourceContent, translatedContent),
								status: "COMPLETED",
								updatedAt: new Date(),
							},
						})
					})

					results.success.push(targetLocale)
				} catch (error) {
					logger.error(`翻译设置到 ${targetLocale} 失败:`, error)

					// 更新失败状态
					await prisma.projectLocaleSiteSetting.upsert({
						where: {
							project_locale_site_setting_unique: {
								projectId,
								locale: targetLocale,
								type,
							},
						},
						update: {
							status: "FAILED",
							error: error instanceof Error ? error.message : String(error),
							updatedAt: new Date(),
						},
						create: {
							projectId,
							locale: targetLocale,
							type,
							content: {}, // 失败时使用空对象
							status: "FAILED",
							error: error instanceof Error ? error.message : String(error),
						},
					})

					results.failed.push({
						locale: targetLocale,
						error: error instanceof Error ? error.message : String(error),
					})
				}
			}),
		)

		// 标记源语言设置为已完成
		await prisma.projectLocaleSiteSetting.update({
			data: {
				status: "COMPLETED",
				updatedAt: new Date(),
			},
			where: {
				id: sourceSettingData.id,
			},
		})

		return {
			success: true,
			results,
		}
	} catch (error) {
		logger.error("翻译设置失败:", error)
		return {
			success: false,
			error: error instanceof Error ? error.message : String(error),
		}
	}
}

function mergeContent(
	type: string,
	sourceContent: any,
	translatedContent: any,
) {
	if (["GameCategories", "ArticleCategories"].includes(type)) {
		return mergeArray(sourceContent, translatedContent, (f: any) => f.code)
	} else if (type === "Nav") {
		return mergeArray(sourceContent, translatedContent, (f: any) => f.id)
	} else {
		return {
			...sourceContent,
			...translatedContent,
		}
	}
}

/**
 * 根据设置类型获取对应的Schema
 *
 * @param type 设置类型
 * @returns 对应的Zod Schema
 */
function getSchemaByType(type: string): z.ZodType<any> {
	// 根据类型返回不同的Schema
	switch (type) {
		case "GameCategories":
		case "ArticleCategories":
			// 分类项的单个对象Schema - 确保返回对象类型而非数组类型
			return z.object({
      code: z.string(),
				name: z.string(),
				metadata: z
					.object({
						title: z.string().optional(),
						description: z.string().optional(),
					})
					.optional(),
				children: z
					.array(
						z.object({
							code: z.string(),
							name: z.string(),
							metadata: z.object({
								title: z.string().optional(),
								description: z.string().optional(),
							}),
						}),
					)
					.optional(),
			})
		case "Nav":
			return z.object({
				id: z.string(),
				label: z.string(),
				children: z.array(
					z
						.object({
							id: z.string(),
							label: z.string(),
						})
						.optional(),
				),
			})

		default:
			// 默认使用通用对象Schema
			return z.object({}).passthrough() // 使用passthrough确保接受任何额外属性
	}
}
