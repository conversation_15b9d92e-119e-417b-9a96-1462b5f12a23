import { NextRequest } from "next/server"
import { prisma } from "@repo/db"
import { R } from "@repo/utils/server"
import { hasProjectAccess, authUser } from "@repo/auth/server"
import { AutoRouter } from "itty-router"
import { z } from "zod"
import { GameType, ProjectGameStatus } from "@repo/shared-types"
import { createTranslationTask } from "@/lib/services/BackgroundTaskService"

const router = AutoRouter({
	base: "/api/project-games",
})

/**
 * 根据游戏内容类型获取需要翻译的字段
 */
function getFieldsToTranslate(type: string): string[] {
	switch (type) {
		case "metadata":
			return [
				"title",
				"name",
				"description",
				"ogTitle",
				"ogDescription",
				"twitterTitle",
				"twitterDescription",
			]
		case "content":
			return ["title", "description", "jsonContent", "text"]
		case "basicInfo":
			return ["gameName", "gameDescription", "name", "description"]
		default:
			// 对于未知类型，尝试翻译常见的文本字段
			return ["title", "name", "description", "content", "text"]
	}
}

/**
 * 判断游戏内容是否需要创建翻译任务
 */
async function shouldCreateGameTranslationTask(
	projectId: string,
	gameId: string,
	type: string,
	locale: string,
	newContent: any,
	existingContentId?: string,
): Promise<{ shouldTranslate: boolean; targetLocales: string[] }> {
	// 获取项目的语言设置
	const project = await prisma.project.findUnique({
		where: { id: projectId },
		include: {
			siteSettings: true,
		},
	})

	if (
		!project?.siteSettings ||
		!Array.isArray(project.siteSettings) ||
		project.siteSettings.length === 0
	) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	const siteSettings = project.siteSettings[0]
	if (!siteSettings) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	const languages = siteSettings.languanges as string[]
	const defaultLanguage = siteSettings.defaultLocale

	if (!languages || !Array.isArray(languages) || languages.length <= 1) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	// 如果当前语言不是默认语言，不创建翻译任务
	if (locale !== defaultLanguage) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	// 获取需要翻译的目标语言
	const targetLocales = languages.filter((lang) => lang !== defaultLanguage)
	if (targetLocales.length === 0) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	// 如果是更新操作，检查内容是否有变化
	if (existingContentId) {
		const existingContent = await prisma.projectGameLocale.findUnique({
			where: { id: existingContentId },
			select: { content: true },
		})

		if (existingContent) {
			// 检查需要翻译的字段是否有变化
			const fieldsToTranslate = getFieldsToTranslate(type)
			const hasChanges = fieldsToTranslate.some((field) => {
				const oldValue = getNestedValue(existingContent.content, field)
				const newValue = getNestedValue(newContent, field)
				return oldValue !== newValue
			})

			if (!hasChanges) {
				return { shouldTranslate: false, targetLocales: [] }
			}
		}
	}

	// 检查其他语言的内容是否为空
	const existingTranslations = await prisma.projectGameLocale.findMany({
		where: {
			projectId,
			gameId,
			type,
			locale: { in: targetLocales },
			...(existingContentId
				? {
						contentId: (
							await prisma.projectGameLocale.findUnique({
								where: { id: existingContentId },
								select: { contentId: true },
							})
						)?.contentId,
					}
				: {}),
		},
		select: { locale: true, content: true },
	})

	// 找出需要翻译的语言（内容为空或不存在的语言）
	const localesNeedingTranslation = targetLocales.filter((targetLocale) => {
		const existingTranslation = existingTranslations.find(
			(t) => t.locale === targetLocale,
		)
		if (!existingTranslation) return true

		// 检查是否有任何需要翻译的字段为空
		const fieldsToTranslate = getFieldsToTranslate(type)
		return fieldsToTranslate.some((field) => {
			const value = getNestedValue(existingTranslation.content, field)
			return !value || (typeof value === "string" && value.trim() === "")
		})
	})

	return {
		shouldTranslate: localesNeedingTranslation.length > 0,
		targetLocales: localesNeedingTranslation,
	}
}

/**
 * 获取嵌套对象的值
 */
function getNestedValue(obj: any, path: string): any {
	if (!obj || typeof obj !== "object") return undefined

	return path.split(".").reduce((current, key) => {
		return current?.[key] || undefined
	}, obj)
}

// 验证schema
const createGameSchema = z.object({
	projectId: z.string(),
	name: z.string().min(1, "游戏名称不能为空"),
	slug: z.string().min(1, "游戏访问路径不能为空"),
	categories: z.array(z.string()).optional().default([]),
	userInputText: z.string().optional(),
	isPrimary: z.boolean().optional().default(false),
	settings: z.any().optional(),
	gameType: z
		.enum([
			GameType.Iframe,
			GameType.Download,
			GameType.Popup,
			GameType.Placeholder,
		])
		.default(GameType.Iframe),
	gameDownloadSettings: z.any().optional(),
	backgroundSettings: z.any().optional(),
	gameIframeUrl: z.string().optional(),
	screenshotUrl: z.string().optional(),
	relatedGames: z.array(z.string()).optional().default([]),
	relatedGamesConfig: z.any().optional(),
	relatedVideos: z
		.array(z.object({ url: z.string() }))
		.optional()
		.default([]),
	relatedArticles: z.array(z.string()).optional().default([]),
	commentsConfig: z.any().optional(),
	tags: z.array(z.string()).optional().default([]),
	status: z.string().optional().default("PENDING"),
})

const updateGameSchema = createGameSchema.partial().extend({
	id: z.string(),
})

// 获取游戏列表
router.get("/page", async (request) => {
	try {
		const {
			projectId,
			search,
			category,
			tag,
			status,
			isPrimary,
			page = 1,
			pageSize = 10,
		} = request.query

		// 验证项目访问权限
		const auth = await hasProjectAccess(projectId as string)

		const take = Math.min(Number(pageSize), 20)
		const skip = (Number(page) - 1) * take

		// 构建查询条件
		const where: any = {
			projectId: projectId as string,
			userId: auth.id,
			...(search && { name: { contains: search as string } }),
			...(status && { status: status as string }),
		}

		// 根据是否主页游戏筛选
		if (isPrimary !== undefined) {
			where.isPrimary = isPrimary === "true"
		}

		// 根据分类筛选
		if (category) {
			where.categories = {
				has: category as string,
			}
		}

		// 根据标签筛选
		if (tag) {
			where.tags = {
				has: tag as string,
			}
		}

		const [games, total] = await Promise.all([
			prisma.projectGame.findMany({
				where,
				skip,
				take,
				orderBy: { createdAt: "desc" },
			}),
			prisma.projectGame.count({ where }),
		])
		return R.ok({
			data: games,
			total,
			page: Number(page),
			pageSize: take,
		})
	} catch (error) {
		console.error("获取游戏列表失败:", error)
		return R.error("获取游戏列表失败")
	}
})

// 获取单个游戏
router.get("/", async (request) => {
	try {
		const auth = await authUser()
		if (!auth)
			return R.raw({ code: 401, message: "未授权访问" }, { status: 401 })

		const { id } = request.query

		// 获取游戏基本信息和相关的本地化内容
		const game = await prisma.projectGame.findUnique({
			where: { id: id as string },
		})

		if (!game) {
			return R.notFound("游戏不存在")
		}

		// 检查权限
		if (game.userId !== auth.id) {
			return R.raw({ code: 403, message: "无权访问该游戏" }, { status: 403 })
		}

		return R.ok(game)
	} catch (error) {
		console.error("获取游戏详情失败:", error)
		return R.error("获取游戏详情失败")
	}
})

// 创建游戏
router.post("/new-game", async (request) => {
	try {
		const body = await request.json()
		const data = createGameSchema.parse(body)
		const { projectId } = data
		// 验证项目访问权限
		const auth = await hasProjectAccess(projectId)
		//判断slug是否属于/games开头，如果不是则添加
		if (!data.slug.startsWith("/games/")) {
			data.slug = `/games/${data.slug}`
		}
		// 检查slug是否已存在
		const existingGame = await prisma.projectGame.findFirst({
			where: {
				projectId: projectId,
				OR: [{ slug: data.slug }, { name: data.name }],
			},
		})

		if (existingGame) {
			return R.bad("游戏访问路径或者游戏名称已存在，并检查是否已存在相同游戏")
		}

		// 统计这个网站当天已经添加的游戏总数量
		const todayStart = new Date()
		todayStart.setHours(0, 0, 0, 0)
		const todayEnd = new Date()
		todayEnd.setHours(23, 59, 59, 999)

		const todayCount = await prisma.projectGame.count({
			where: {
				projectId,
				status: {
					in: [ProjectGameStatus.Completed, ProjectGameStatus.Pending],
				},
				createdAt: {
					gte: todayStart,
					lte: todayEnd,
				},
			},
		})

		if (todayCount + 1 > 20) {
			return R.error(
				`每天最多只能创建20个游戏，今天已经添加了${todayCount}个，不建议一次性添加过多游戏导致Google索引问题`,
			)
		}
		// 检查是否有主游戏，如果没有就设置为第一个游戏
		const existingPrimaryGame = await prisma.projectGame.findFirst({
			where: {
				projectId,
				isPrimary: true,
			},
		})
		if (!existingPrimaryGame) {
			console.log(`没有主游戏，设置:${data.name} 为主游戏`)
			data.isPrimary = true
		}

		// 创建游戏
		const game = await prisma.projectGame.create({
			data: {
				...data,
				status: ProjectGameStatus.Completed,
				userId: auth.id,
			},
		})

		return R.ok(game)
	} catch (error) {
		console.error("创建游戏失败:", error)
		if (error instanceof z.ZodError) {
			return R.bad(error.errors[0]?.message || "创建游戏失败")
		}
		return R.error("创建游戏失败")
	}
})

// 设置主页游戏
router.put("/set-primary-game", async (request) => {
	try {
		const auth = await authUser()
		if (!auth)
			return R.raw({ code: 401, message: "未授权访问" }, { status: 401 })

		const body = await request.json()
		const { gameId, projectId } = body

		if (!gameId || !projectId) {
			return R.bad("缺少必要参数")
		}

		// 检查游戏是否存在
		const existingGame = await prisma.projectGame.findUnique({
			where: { id: gameId },
		})

		if (!existingGame) {
			return R.notFound("游戏不存在")
		}

		// 检查权限
		if (existingGame.userId !== auth.id) {
			return R.forbidden()
		}

		// 检查项目ID是否匹配
		if (existingGame.projectId !== projectId) {
			return R.bad("游戏不属于指定项目")
		}

		// 先将所有游戏的isPrimary设为false
		await prisma.projectGame.updateMany({
			where: {
				projectId,
				isPrimary: true,
			},
			data: {
				isPrimary: false,
			},
		})

		// 将当前游戏设为主页游戏
		const updatedGame = await prisma.projectGame.update({
			where: { id: gameId },
			data: {
				isPrimary: true,
			},
		})

		return R.ok(updatedGame)
	} catch (error) {
		console.error("设置主页游戏失败:", error)
		return R.error("设置主页游戏失败")
	}
})

// 更新游戏
router.put("/update-game", async (request) => {
	try {
		const auth = await authUser()
		if (!auth)
			return R.raw({ code: 401, message: "未授权访问" }, { status: 401 })

		const body = await request.json()
		const data = updateGameSchema.parse({ ...body, id: body.gameId })

		// 检查游戏是否存在
		const existingGame = await prisma.projectGame.findUnique({
			where: { id: data.id },
		})

		if (!existingGame) {
			return R.notFound("游戏不存在")
		}
		//判断slug是否属于/games开头，如果不是则添加
		if (data.slug && !data.slug.startsWith("/games/")) {
			data.slug = `/games/${data.slug}`
		}
		// 如果要更新slug，检查新slug是否已存在
		if (data.slug && data.slug !== existingGame.slug) {
			const slugExists = await prisma.projectGame.findFirst({
				where: {
					projectId: existingGame.projectId,
					slug: data.slug,
					id: { not: data.id }, // 排除当前游戏
				},
			})

			if (slugExists) {
				return R.bad("游戏访问路径已存在，请使用其他路径")
			}
		}

		// 移除id字段，因为它不应该被更新
		const { id: _, ...updateData } = data

		const updatedGame = await prisma.projectGame.update({
			where: { id: data.id },
			data: updateData,
		})

		return R.ok(updatedGame)
	} catch (error) {
		console.error("更新游戏失败:", error)
		if (error instanceof z.ZodError) {
			return R.bad(error.errors[0]?.message || "更新游戏失败")
		}
		return R.error("更新游戏失败")
	}
})

// 删除游戏
router.delete("/delete-game", async (request) => {
	try {
		const auth = await authUser()
		if (!auth)
			return R.raw({ code: 401, message: "未授权访问" }, { status: 401 })

		const { gameId } = request.query

		// 检查游戏是否存在
		const existingGame = await prisma.projectGame.findUnique({
			where: { id: gameId as string },
		})

		if (!existingGame) {
			return R.notFound("游戏不存在")
		}

		// 检查是否是首页游戏
		if (existingGame.isPrimary) {
			return R.bad("首页游戏不能删除，请先将其他游戏设置为首页游戏后再删除")
		}

		// 删除游戏及其关联的本地化内容
		await prisma.$transaction([
			prisma.projectGameLocale.deleteMany({
				where: { gameId: gameId as string },
			}),
			prisma.projectGame.delete({
				where: { id: gameId as string },
			}),
		])

		return R.ok({ message: "游戏删除成功" })
	} catch (error) {
		console.error("删除游戏失败:", error)
		return R.error("删除游戏失败")
	}
})

// 获取游戏内容
router.get("/content/list", async (request) => {
	try {
		const auth = await authUser()
		if (!auth) return R.unauthorized()

		const { projectId, gameId, locale, type } = request.query

		if (!projectId || !gameId) {
			return R.bad("缺少必要参数")
		}

		// 检查游戏是否存在
		const existingGame = await prisma.projectGame.findUnique({
			where: { id: gameId as string },
		})

		if (!existingGame) {
			console.log("游戏不存在")
			return R.bad("游戏不存在")
		}

		// 检查权限
		if (existingGame.userId !== auth.id) {
			return R.forbidden()
		}

		// 检查项目ID是否匹配
		if (existingGame.projectId !== projectId) {
			return R.bad("游戏不属于指定项目")
		}

		// 获取游戏内容
		const gameContents = await prisma.projectGameLocale.findMany({
			where: {
				projectId: projectId as string,
				gameId: gameId as string,
				locale: locale as string,
				type: type as string,
			},
			orderBy: [{ sort: "desc" }],
		})

		return R.ok(gameContents)
	} catch (error) {
		console.error("获取游戏内容失败:", error)
		return R.error("获取游戏内容失败")
	}
})

// 获取游戏内容
router.get("/content", async (request) => {
	try {
		const auth = await authUser()
		if (!auth) return R.unauthorized()

		const { locale, id } = request.query

		if (!id || !locale) {
			return R.bad("缺少必要参数")
		}

		// 检查游戏是否存在
		const content = await prisma.projectGameLocale.findUnique({
			where: {
				project_game_locale_content_id_locale_unique: {
					contentId: id as string,
					locale: locale as string,
				},
			},
		})
		return R.ok(content)
	} catch (error) {
		console.error("获取游戏内容失败:", error)
		return R.error("获取游戏内容失败")
	}
})

// 保存游戏内容
router.post("/content", async (request) => {
	try {
		const auth = await authUser()
		if (!auth)
			return R.raw({ code: 401, message: "未授权访问" }, { status: 401 })

		const body = await request.json()
		const { projectId, gameId, locale, type, content, sort = 0, id } = body

		if (!projectId || !gameId || !locale || !type) {
			return R.bad("缺少必要参数")
		}

		// 检查游戏是否存在
		const existingGame = await prisma.projectGame.findUnique({
			where: { id: gameId },
		})

		if (!existingGame) {
			return R.notFound("游戏不存在")
		}

		// 检查项目ID是否匹配
		if (existingGame.projectId !== projectId) {
			return R.bad("游戏不属于指定项目")
		}

		let savedContent: any = null
		let translationTaskId: string | null = null

		await prisma.$transaction(async (tx) => {
			if (id) {
				savedContent = await tx.projectGameLocale.update({
					where: { id },
					data: { ...body, status: ProjectGameStatus.Completed },
				})
			} else {
				savedContent = await tx.projectGameLocale.create({
					data: { ...body, status: ProjectGameStatus.Completed },
				})
			}
		})

		// 如果需要创建翻译任务，检查是否应该创建
		if (savedContent) {
			try {
				const shouldTranslate = await shouldCreateGameTranslationTask(
					projectId,
					gameId,
					type,
					locale,
					content,
					id, // 如果是更新操作，传入现有内容ID
				)

				if (shouldTranslate.shouldTranslate) {
					translationTaskId = await createTranslationTask(
						type,
						projectId,
						auth.id,
						{
							contentType: `ProjectGameLocale_${type}`,
							contentId: savedContent.id,
							sourceLocale: locale,
							targetLocales: shouldTranslate.targetLocales,
							fieldsToTranslate: getFieldsToTranslate(type),
							gameId: gameId,
							gameName: existingGame.name,
						},
					)
				}
			} catch (error) {
				console.error("创建翻译任务失败:", error)
				// 不影响主要的保存操作，只记录错误
			}
		}

		return R.ok({
			content: savedContent,
			translationTaskId,
		})
	} catch (error) {
		console.error("保存游戏内容失败:", error)
		return R.error("保存游戏内容失败")
	}
})

// 保存游戏内容
router.post("/content/list", async (request) => {
	try {
		const auth = await authUser()
		if (!auth)
			return R.raw({ code: 401, message: "未授权访问" }, { status: 401 })

		const body = await request.json()
		const { projectId, gameId, locale, type, contents } = body

		if (!projectId || !gameId || !locale || !type) {
			return R.bad("缺少必要参数")
		}

		// 检查游戏是否存在
		const existingGame = await prisma.projectGame.findUnique({
			where: { id: gameId },
		})

		if (!existingGame) {
			return R.notFound("游戏不存在")
		}

		// 检查权限
		if (existingGame.userId !== auth.id) {
			return R.raw(
				{ code: 403, message: "无权修改该游戏内容" },
				{ status: 403 },
			)
		}

		// 检查项目ID是否匹配
		if (existingGame.projectId !== projectId) {
			return R.bad("游戏不属于指定项目")
		}

		// 确保contents是数组
		if (!Array.isArray(contents)) {
			return R.bad("contents必须是数组")
		}

		// 为每个内容项添加排序值，从99开始递减
		const sortedContents = contents.map((item, index) => {
			// 计算排序值：最大值为99，依次递减
			const sortOrder = Math.max(99 - index, 1) // 确保最小值为1
			return {
				...item,
				sort: sortOrder,
			}
		})

		// 处理批量内容操作
		const results = []
		const translationTasks = []

		for (const item of sortedContents) {
			const { id, operation, sort, ...contentFields } = item

			// 使用解构赋值来提取内容字段，避免使用delete操作符
			const content = contentFields

			// 根据操作类型执行不同的数据库操作
			if (operation === "delete") {
				await prisma.projectGameLocale.delete({
					where: { id },
				})
				results.push({ id, operation: "delete", success: true })
			} else if (operation === "create") {
				// 创建新记录
				const newContent = await prisma.projectGameLocale.create({
					data: {
						projectId,
						gameId,
						type,
						locale,
						content,
						sort, // 添加排序值
						status: ProjectGameStatus.Completed,
					},
				})
				results.push({ ...newContent, operation: "create" })

				translationTasks.push({
					contentId: newContent.id,
					content: content,
					operation: "create",
				})
			} else {
				// 更新现有记录
				const updatedContent = await prisma.projectGameLocale.update({
					where: { id },
					data: {
						content,
						sort, // 添加排序值
						status: ProjectGameStatus.Completed,
						updatedAt: new Date(),
					},
				})
				results.push({ ...updatedContent, operation: "update" })

				translationTasks.push({
					contentId: updatedContent.id,
					content: content,
					operation: "update",
				})
			}
		}

		// 处理翻译任务
		const createdTranslationTasks = []
		if (translationTasks.length > 0) {
			for (const task of translationTasks) {
				try {
					const shouldTranslate = await shouldCreateGameTranslationTask(
						projectId,
						gameId,
						type,
						locale,
						task.content,
						task.operation === "update" ? task.contentId : undefined,
					)

					if (shouldTranslate.shouldTranslate) {
						const translationTaskId = await createTranslationTask(
							type,
							projectId,
							auth.id,
							{
								contentType: `ProjectGameLocale_${type}`,
								contentId: task.contentId,
								sourceLocale: locale,
								targetLocales: shouldTranslate.targetLocales,
								fieldsToTranslate: getFieldsToTranslate(type),
								gameId: gameId,
								gameName: existingGame.name,
							},
						)
						createdTranslationTasks.push(translationTaskId)
					}
				} catch (error) {
					console.error("创建翻译任务失败:", error)
					// 不影响主要的保存操作，只记录错误
				}
			}
		}

		return R.ok({
			results,
			translationTasks: createdTranslationTasks,
		})
	} catch (error) {
		console.error("保存游戏内容失败:", error)
		return R.error("保存游戏内容失败")
	}
})

// 导出HTTP方法处理函数
export const GET = async (request: NextRequest) => router.fetch(request)
export const POST = async (request: NextRequest) => router.fetch(request)
export const PUT = async (request: NextRequest) => router.fetch(request)
export const DELETE = async (request: NextRequest) => router.fetch(request)
