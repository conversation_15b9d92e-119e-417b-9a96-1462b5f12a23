"use client"

import { Language, ProjectLanguage } from "@repo/shared-types"
import { cn } from "@repo/utils/react"
import { memo, useCallback, useMemo } from "react"
import { useTranslationContext } from "./TranslationContext"

interface TranslationSwitcherProps {
	onLanguageChange?: (language: ProjectLanguage) => void
}

type LanguageButtonProps = {
	language: Omit<Language, "localName">
	isActive: boolean
	onClick: (code: ProjectLanguage) => void
}

// 语言按钮组件，使用memo避免不必要的重新渲染
const LanguageButton = memo(
	({ language, isActive, onClick }: LanguageButtonProps) => {
		// 使用useCallback缓存点击处理函数
		const handleClick = useCallback(() => {
			onClick(language.code as ProjectLanguage)
		}, [onClick, language.code])

		return (
			<button
				type="button"
				className={cn(
					"px-2 text-xs focus:outline-none transition-colors select-none cursor-pointer",
					isActive
						? "border-b border-primary text-primary"
						: "text-gray-600 hover:text-primary",
				)}
				onClick={handleClick}
			>
				{language.name}
			</button>
		)
	},
	// 自定义比较函数，只有当语言代码或激活状态变化时才重新渲染
	(prevProps, nextProps) => {
		return (
			prevProps.language.code === nextProps.language.code &&
			prevProps.isActive === nextProps.isActive
		)
	},
)

// 为组件添加displayName，便于调试
LanguageButton.displayName = "LanguageButton"

const TranslationSwitcher = memo(
	({ onLanguageChange: propOnLanguageChange }: TranslationSwitcherProps) => {
		// 使用context中的语言设置
		const { defaultLanguage, currentLanguage, languages, setCurrentLanguage } =
			useTranslationContext()

		// 使用useMemo缓存默认语言对象
		const defaultLang = useMemo(
			() => languages.find((lang) => lang.code === defaultLanguage),
			[languages, defaultLanguage],
		)

		// 使用useCallback缓存语言选择处理函数
		const handleLanguageSelect = useCallback(
			(code: ProjectLanguage) => {
				// 只有当选择的语言与当前语言不同时才更新
				if (code !== currentLanguage) {
					setCurrentLanguage(code)
					propOnLanguageChange?.(code)
				}
			},
			[currentLanguage, setCurrentLanguage, propOnLanguageChange],
		)

		return (
			<div className="flex flex-col gap-1 justify-center items-end">
				<div className="flex items-center gap-2 mb-1">
					<div>
						<span className="text-xs mr-1">默认语言：</span>
						<span className="select-none text-xs text-primary bg-primary/5 border border-primary/30 rounded-md px-2 py-1">
							{defaultLang?.name}
						</span>
					</div>
				</div>

				<div className="flex">
					<div className="flex flex-wrap">
						{languages.map((language) => (
							<LanguageButton
								key={language.code}
								language={language}
								isActive={currentLanguage === language.code}
								onClick={handleLanguageSelect}
							/>
						))}
					</div>
				</div>
			</div>
		)
	},
)

TranslationSwitcher.displayName = "TranslationSwitcher"

export { TranslationSwitcher }

// 使用memo包装整个组件，避免父组件重新渲染时不必要的重新渲染
export default TranslationSwitcher
