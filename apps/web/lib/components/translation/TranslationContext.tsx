"use client"

import {
	createContext,
	useContext,
	useState,
	useCallback,
	useMemo,
	ReactNode,
	memo,
} from "react"

import {
	availableLanguages,
	ProjectLanguage,
	Language,
} from "@repo/shared-types"
import { toast } from "sonner"
// 定义业务参数类型，可以根据实际需求扩展
export interface TranslationParams {
	[key: string]: any
}

interface TranslationContextType {
	languages: Omit<Language, "localName">[]
	defaultLanguage: ProjectLanguage
	currentLanguage: ProjectLanguage
	setCurrentLanguage: (code: ProjectLanguage) => void
}

const defaultLanguages: Omit<Language, "localName">[] = availableLanguages

const TranslationContext = createContext<TranslationContextType>({
	languages: defaultLanguages,
	defaultLanguage: ProjectLanguage.EN,
	currentLanguage: ProjectLanguage.EN,
	setCurrentLanguage: () => {},
})

export function useTranslationContext() {
	return useContext(TranslationContext)
}

interface TranslationProviderProps {
	children: ReactNode
	defaultLanguage?: ProjectLanguage
	languages?: Omit<Language, "localName">[]
	onLanguageChange?: (code: ProjectLanguage) => void
}

export const TranslationProvider = memo(function TranslationProvider({
	children,
	defaultLanguage = ProjectLanguage.EN,
	languages = defaultLanguages,
	onLanguageChange,
}: TranslationProviderProps) {
	// 使用useState管理当前语言
	const [currentLanguage, setCurrentLanguage] =
		useState<ProjectLanguage>(defaultLanguage)

	// 使用useCallback缓存语言变更处理函数，避免不必要的重新渲染
	const handleLanguageChange = useCallback(
		(code: ProjectLanguage) => {
			// 只有当选择的语言与当前语言不同时才更新
			if (code !== currentLanguage) {
				setCurrentLanguage(code)
				onLanguageChange?.(code)
			}
		},
		[currentLanguage, onLanguageChange],
	)

	// 使用useMemo缓存context值，避免每次渲染都创建新对象
	const contextValue = useMemo(
		() => ({
			languages,
			defaultLanguage,
			currentLanguage,
			setCurrentLanguage: handleLanguageChange,
		}),
		[languages, defaultLanguage, currentLanguage, handleLanguageChange],
	)

	return (
		<TranslationContext.Provider value={contextValue}>
			{children}
		</TranslationContext.Provider>
	)
})
