/**
 * API客户端类型定义
 * 包含所有与API交互相关的类型定义
 */
export enum ProjectLocaleSiteSettingType {
  // 元数据
  Metadata = 'Metadata',
  // 导航
  Nav = 'Nav',
  // 游戏分类
  GameCategories = 'GameCategories',
  // 文章分类
  ArticleCategories = 'ArticleCategories',
}

/**
 * 项目游戏详情内容类型
 */
export enum ProjectGameLocaleType {
  // @see api-types.ts MetadataInfo
  Metadata = 'metadata',
  // 游戏内容（包含游戏玩法、攻略、截图、FAQ等，使用json格式存放每种语言的所有内容）
  Content = 'content',
  // 游戏基本信息（比如需要国际化的名称，游戏描述等）
  BasicInfo = 'basicInfo',
}

/**
 * 游戏类型
 */
export enum GameType {
  // iframe嵌入
  Iframe = 'iframe',
  // 下载
  Download = 'download',
  // 弹窗
  Popup = 'popup',
  // 占位类型（没有任何可操作的内容，用于占位）
  Placeholder = 'placeholder',
}

/**
 * 游戏详情内容类型(根据这个类型来渲染不同的内容)
 */
export enum GameDetailContentType {
  // 推荐游戏
  RelatedGames = 'relatedGames',
  // 相关视频
  RelatedVideos = 'relatedVideos',
  // 文章
  Article = 'article',
  // 评论
  Comments = 'comments',
}

/**
 * 网站设置信息接口(与数据库结构保持一致，全局配置，与多语言无关)
 */
export interface SiteSettings {
  // 网站名称
  siteName: string;
  // 网站模板类型
  templateType: string;
  // 网站Logo
  logo: string;
  // 暗色模式Logo
  darkLogo?: string;
  // 网站域名
  domain?: string;
  // 联系邮箱
  contactEmail?: string;
  // 社交媒体链接
  socialLinks?: SocialLinksConfig;
  // 分析工具配置
  analytics?: AnalyticsConfig;
  // 字体配置
  fonts?: FontsConfig;
  // 主题配置
  theme?: ThemeConfig;
  // 支持的语言列表
  languanges: string[];
  // 语言名称映射(key为语言代码，value为语言类型对象)
  languangesNames: Record<string, Language>;
  // 默认语言
  defaultLocale: string;
  // 自定义头部内容
  customHeaderContent?: string;
  // ads.txt 内容
  adsTxtContent?: string;
  // 头部和底部数据在模板页面渲染时动态生成
  // 图标和图片
  icons?: IconsConfig;
  // 友情链接
  friendLinks?: FriendLink[];
}

// 定义导航项类型
export interface NavItem {
  id: string;
  label: string;
  href: string;
  target?: string;
  iconName?: string; // 图标名称，用于动态选择图标
  children?: NavItem[]; // 子菜单项
}

/**
 * 语言类型
 */
export type Language = {
  // 语言代码
  code: string;
  // 中文语言名称
  name: string;
  // 本地语言名称
  localName: string;
};
/**
 * 友情链接类型
 */
export interface FriendLink {
  name: string;
  url: string;
}

// 这些类型在模板页面渲染时动态生成，但保留接口定义以兼容现有代码
export interface BreadcrumbItem {
  label: string;
  href: string;
  isActive?: boolean;
}

// 定义元数据类型
export interface MetadataInfo {
  // 标题（网页标题、SEO使用的标题）
  title: string;
  // 名称（用于需要国际化的名称，如果游戏名称，与title不一定相同）
  name?: string;
  // 标语（游戏标语或口号）
  slogan?: string;
  // 描述
  description: string;
  // 社交分享标题
  ogTitle?: string;
  // 社交分享描述
  ogDescription?: string;
  // 社交分享图片
  ogImage?: string;
  // 社交分享链接
  ogUrl?: string;
  // 社交分享类型
  twitterCard?: string;
  // 社交分享标题
  twitterTitle?: string;
  // 社交分享描述
  twitterDescription?: string;
  // 社交分享图片
  twitterImage?: string;
  // 结构化数据
  structuredData?: Record<string, any>;
}

// 定义Tab项类型
export interface TabItem {
  id: string;
  label: string;
  iconName?: string;
}

// 定义评论类型
export interface GameComment {
  // 评论ID
  id: string;
  // 评论者
  author: string;
  // 评论时间
  timestamp: string;
  // 评论内容
  text: string;
  // 评论评分
  rating?: number;
  // 点赞数
  likes: number;
  // 不喜欢数
  dislikes: number;
  // 回复
  replies?: GameComment[];
}

// 定义游戏信息类型(不区分多语言)
export interface ProjectGame {
  id: string;
  // 游戏访问路径
  slug: string;
  // 游戏名称（未国际化的名称）
  name: string;
  // 是否是主页游戏
  isPrimary?: boolean;
  // 游戏信息
  gameInfo: GameInfoData;
  // 游戏类型 iframe|download|popup|plachholder
  gameType?: GameType;
  // 游戏标签（标签Id集合，根据标签Id关联标签详情）
  tags?: string[];
  // 游戏分类（分类Id集合，根据分类Id关联分类详情）
  categories?: string[];
  // 游戏图片（第一个为静态图用于列表展示、第二个可以是视频截图或者gif(允许为空））
  gameImages?: string[];
  // 游戏视频列表
  relatedVideos?: GameVideo[];
  // 相关文章列表(文章ID集合，根据ID关联文章详情)
  relatedArticles?: string[];
  // 相关游戏列表(游戏ID集合，根据ID关联游戏详情)
  relatedGames?: string[];
  // 更新时间
  updateTime?: Date;
  // 评论配置
  commentsConfig?: CommentsConfig;
  // 多语言游戏详情内容(每个语言一个对象)
  gameLocales?: GameLocaleArray[];
}

/**
 * 多语言游戏详情内容（根据语言区分）
 */
export interface GameLocaleArray {
  locale: string;
  content: GameLocaleContent;
}

/**
 * 多语言游戏详情内容（根据语言区分）
 */
export interface GameLocaleContent {
  // 游戏ID（冗余，便于其他接口和页面使用）
  id: string;
  // 游戏更新时间(冗余内容，便于其他接口和页面使用)
  updateTime: string;
  // 游戏访问路径（冗余，便于其他接口和页面使用）
  slug: string;
  // 游戏图片（冗余内容，第一个为静态图用于列表展示、第二个可以是视频截图或者gif(允许为空））
  gameImages?: string[];
  // 游戏标签(冗余内容，便于其他接口和页面使用，api-client不会返回，需要页面调用tag接口获取后传入)
  gameTags?: GameTag[];
  // 游戏分类（分类Id集合，根据分类Id关联分类详情）
  categories?: string[];
  // 游戏标签（标签Id集合，根据标签Id关联标签详情）
  tags?: string[];
  // 游戏信息(冗余内容，便于其他接口和页面使用)
  gameInfo: GameInfoData;
  // 游戏名称(国际化后的名称)
  gameName: string;
  // 游戏标语(国际化后的标语)
  gameSlogan: string;
  // 游戏描述(国际化后的描述)
  gameDescription: string;
  // 游戏元数据(SEO相关信息)
  metadata: MetadataInfo;
  // 面包屑(用于面包屑导航，不需要包含首页)
  breadcrumbItems?: BreadcrumbItem[];
  // 游戏详情内容列表(根据type区分)
  contents: GameDetailContent[];
}

/**
 * 游戏详情内容类型
 */
export interface GameDetailContent {
  tabId: string;
  // 内容类型
  type: GameDetailContentType;
  // 标题（用于tab显示）
  title?: string;
  // 图标（用于tab显示）
  icon?: string;
  // 文本内容
  text?: string;
  // JSON内容
  jsonContent?: object;
}

export type GameInfoSettings = Partial<{
  developer?: string;
  // 发布日期
  releaseDate?: string;
  // 最后更新日期
  lastUpdate?: string;
  // 版本
  version?: string;
  // 大小
  size?: string;
  // 平台
  platform?: string;
  // 年龄评级
  ageRating?: string;
  // 游戏评分 (0-5分)
  rating?: number;
}> &
  Record<string, any>;

/**
 * 游戏信息数据
 */
export interface GameInfoData {
  /**
   *  根据游戏类型不同，可能需要展示不同的信息。
   *  模板当中已经默认一些Game开头的国际化内容字段，用于匹配这个key值在页面上国际化显示
   * 如：Game.developer
  developer?: string;
  // 发布日期
  releaseDate?: string;
  // 最后更新日期
  lastUpdate?: string;
  // 描述
  description?: string;
  // 版本
  version?: string;
  // 大小
  size?: string;
  // 平台
  platform?: string;
  // 年龄评级
  ageRating?: string;
   *
  */
  settings: GameInfoSettings;
  // 游戏ID
  id: string;
  // 游戏访问路径
  gameUrl: string;
  // 游戏下载配置
  gameDownload?: GameDownloadSettings;
  // 游戏区域的背景配置
  background: GameBackgroundSettings;
}

/**
 * 游戏下载配置
 */
export interface GameDownloadSettings {
  showDownloadButton: boolean;
  downloadUrls: {
    ios: string;
    android: string;
    pc: string;
    steam: string;
    mac: string;
  };
}

/**
 * 游戏区域背景配置
 */
export interface GameBackgroundSettings {
  show: boolean;
  type: string;
  imageUrl?: string;
  videoUrl?: string;
}

/**
 * 游戏标签列表(某个项目下的所有可用标签列表，根据语言区分)
 */
export interface GameTagList {
  locale: string;
  tags: GameTag[];
}

/**
 * 游戏标签
 */
export interface GameTag {
  id: string;
  name: string;
  locale: string;
  slug: string;
  description?: string;
  imageUrl?: string;
  iconName?: string;
  count?: number;
  metaTitle?: string;
  metaDescription?: string;
}

/**
 * 评论配置(暂定)
 */
export interface CommentsConfig {
  // 是否显示评论
  showComments: boolean;
  // 评论系统配置
  commentsSystemConfig: {
    // 评论系统类型
    type: string;
    // 评论系统配置
    config: Record<string, any>;
  };
  // 评论历史记录
  history?: GameComment[];
}

/**
 * 游戏分类
 */
export interface GameCategory {
  code: string;
  parentCode?: string;
  name: string;
  // 所属语言（冗余字段）
  locale: string;
  slug: string;
  // 分类图标（支持icon\image）
  icon?: string;
  // 分类下的游戏数量
  count?: number;
  // 分类排序(数字越大越靠前)
  sortOrder?: number;
  // 分类元数据
  metadata?: MetadataInfo;
  // 子分类
  children?: GameCategory[];
  // 分类设置
  settings?: {
    // 显示样式
    displayStyle: string;
    // 是否显示游戏数量
    showCount: boolean;
    // 是否显示分类图标
    showIcon: boolean;
  };
  // 分类下的游戏列表（冗余字段，用于分类页面和盒子游戏模板）
  games?: GameLocaleContent[];
}

// 定义广告代码类型
export interface AdCode {
  id: string;
  position: string;
  code: string;
}

// 定义Tab内容类型
export interface TabContent {
  // 用于关联游戏详情内容块
  id: string;
  title: string;
  icon?: string;
  sortOrder: number;
  // tab类型：relatedGames|relatedVideos|article
  type: string;
}

// 定义游戏视频类型
export interface GameVideo {
  url: string;
}

// 定义博客文章数据类型
export interface ArticlePost {
  id: string;
  slug: string;
  title: string;
  locale: string;
  titleImageUrl: string;
  category?: ArticleCategory;
  author?: string;
  readTime?: string;
  authorImageUrl?: string;
  updateTime: string;
  tags?: string[];
  relatedPosts?: ArticlePost[];
  mdxContent: string;
  metadata: MetadataInfo;
}

// 定义博客分类数据类型
export interface ArticleCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  count: number;
  image?: string;
}

/**
 * 通用API请求参数接口
 */
export interface ApiRequestParams {
  locale?: string;
  projectId?: string;
  [key: string]: any;
}

/**
 * 游戏列表数据接口
 * 包含各种类型的游戏列表
 */
export interface GameListData {
  // 推荐游戏列表
  games: GameLocaleContent[];
  // 相似游戏列表
  similarGames: GameLocaleContent[];
  // 所有可用游戏
  availableGames: GameLocaleContent[];
  // 分页信息
  pagination?: {
    total: number;
    page: number;
    pageSize: number;
  };
}

/**
 * 图标配置接口
 */
export interface IconsConfig {
  favicon?: string;
  appleTouchIcon?: string;
  androidIcon?: string;
}

/**
 * 分析工具配置接口
 */
export interface AnalyticsConfig {
  gaId?: string;
  clarityId?: string;
  plausible?: string;
  adsenseClientId?: string;
}

/**
 * 社交媒体链接配置接口
 */
export interface SocialLinksConfig {
  twitter?: string;
  facebook?: string;
  instagram?: string;
  youtube?: string;
  linkedin?: string;
}

/**
 * 字体配置接口
 */
export interface FontsConfig {
  sans?: string;
  serif?: string;
  mono?: string;
}

/**
 * 主题配置接口
 */
export interface ThemeConfig {
  // 主题名称
  name?: string;
  // 主色调
  mainColor?: string;
  // 次色调
  secondaryColor?: string;
}

/**
 * 分类数据接口（用于某个分类页面）
 * 包含分类相关的所有数据
 */
export interface CategoriesData {
  /** 所有分类列表 */
  categories: {
    // 分类信息
    category: GameCategory;
    // 分类游戏列表
    games: GameLocaleContent[];
  }[];
}
