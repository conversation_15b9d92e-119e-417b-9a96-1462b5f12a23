import { describe, expect, it } from "vitest"
import { diffObject } from "../diffObject"

describe("diffObject", () => {
  describe("基础对象对比", () => {
    it("应该正确识别对象的差异", () => {
      const obj1 = { a: 1, b: 2, c: 3 }
      const obj2 = { a: 1, b: 3, d: 4 }
      const result = diffObject(obj1, obj2)

      expect(result.added).toEqual({ d: 4 })
      expect(result.removed).toEqual({ c: 3 })
      expect(result.unchanged).toEqual({ a: 1 })
      expect(result.modified).toEqual({
        b: { oldValue: 2, newValue: 3, path: "b" }
      })
      expect(result.hasDifferences).toBe(true)
    })

    it("应该处理相同的对象", () => {
      const obj = { a: 1, b: 2, c: 3 }
      const result = diffObject(obj, obj)

      expect(result.added).toEqual({})
      expect(result.removed).toEqual({})
      expect(result.modified).toEqual({})
      expect(result.unchanged).toEqual({ a: 1, b: 2, c: 3 })
      expect(result.hasDifferences).toBe(false)
    })
  })

  describe("深层对象对比", () => {
    it("应该支持嵌套对象的对比", () => {
      const obj1 = {
        user: { name: "Alice", age: 25 },
        settings: { theme: "dark" }
      }
      const obj2 = {
        user: { name: "Alice", age: 26 },
        settings: { theme: "light", lang: "en" }
      }
      const result = diffObject(obj1, obj2)

      expect(result.added).toEqual({ "settings.lang": "en" })
      expect(result.removed).toEqual({})
      expect(result.unchanged).toEqual({ "user.name": "Alice" })
      expect(result.modified).toEqual({
        "user.age": { oldValue: 25, newValue: 26, path: "user.age" },
        "settings.theme": { oldValue: "dark", newValue: "light", path: "settings.theme" }
      })
      expect(result.hasDifferences).toBe(true)
    })

    it("应该支持浅层对比模式", () => {
      const obj1 = {
        user: { name: "Alice", age: 25 },
        count: 1
      }
      const obj2 = {
        user: { name: "Alice", age: 26 },
        count: 2
      }
      const result = diffObject(obj1, obj2, { deep: false })

      expect(result.added).toEqual({})
      expect(result.removed).toEqual({})
      expect(result.unchanged).toEqual({})
      expect(result.modified).toEqual({
        user: {
          oldValue: { name: "Alice", age: 25 },
          newValue: { name: "Alice", age: 26 },
          path: "user"
        },
        count: { oldValue: 1, newValue: 2, path: "count" }
      })
      expect(result.hasDifferences).toBe(true)
    })
  })

  describe("路径过滤", () => {
    it("应该支持忽略指定路径", () => {
      const obj1 = { a: 1, b: 2, c: 3 }
      const obj2 = { a: 1, b: 3, d: 4 }
      const result = diffObject(obj1, obj2, {
        ignorePaths: ["b", "c"]
      })

      expect(result.added).toEqual({ d: 4 })
      expect(result.removed).toEqual({})
      expect(result.unchanged).toEqual({ a: 1 })
      expect(result.modified).toEqual({})
      expect(result.hasDifferences).toBe(true)
    })

    it("应该支持包含指定路径", () => {
      const obj1 = { a: 1, b: 2, c: 3 }
      const obj2 = { a: 1, b: 3, d: 4 }
      const result = diffObject(obj1, obj2, {
        includePaths: ["a", "b"]
      })

      expect(result.added).toEqual({})
      expect(result.removed).toEqual({})
      expect(result.unchanged).toEqual({ a: 1 })
      expect(result.modified).toEqual({
        b: { oldValue: 2, newValue: 3, path: "b" }
      })
      expect(result.hasDifferences).toBe(true)
    })

    it("应该支持通配符路径匹配", () => {
      const obj1 = {
        user: { profile: { name: "Alice" }, settings: { theme: "dark" } },
        admin: { name: "Bob" }
      }
      const obj2 = {
        user: { profile: { name: "Alice Updated" }, settings: { theme: "light" } },
        admin: { name: "Bob Updated" }
      }
      const result = diffObject(obj1, obj2, {
        ignorePaths: ["user.*"]
      })

      expect(result.added).toEqual({})
      expect(result.removed).toEqual({})
      expect(result.unchanged).toEqual({})
      expect(result.modified).toEqual({
        "admin.name": { oldValue: "Bob", newValue: "Bob Updated", path: "admin.name" }
      })
      expect(result.hasDifferences).toBe(true)
    })
  })

  describe("自定义比较器", () => {
    it("应该支持自定义比较函数", () => {
      const obj1 = { price: 10.1, name: "Product" }
      const obj2 = { price: 10.15, name: "Product Updated" }
      const result = diffObject(obj1, obj2, {
        customComparers: {
          price: (oldVal, newVal) => Math.abs(oldVal - newVal) < 0.1
        }
      })

      expect(result.added).toEqual({})
      expect(result.removed).toEqual({})
      expect(result.unchanged).toEqual({ price: 10.1 })
      expect(result.modified).toEqual({
        name: { oldValue: "Product", newValue: "Product Updated", path: "name" }
      })
      expect(result.hasDifferences).toBe(true)
    })
  })

  describe("边界情况", () => {
    it("应该处理空对象", () => {
      const result1 = diffObject({}, { a: 1, b: 2 })
      expect(result1.added).toEqual({ a: 1, b: 2 })
      expect(result1.removed).toEqual({})
      expect(result1.unchanged).toEqual({})
      expect(result1.modified).toEqual({})
      expect(result1.hasDifferences).toBe(true)

      const result2 = diffObject({ a: 1, b: 2 }, {})
      expect(result2.added).toEqual({})
      expect(result2.removed).toEqual({ a: 1, b: 2 })
      expect(result2.unchanged).toEqual({})
      expect(result2.modified).toEqual({})
      expect(result2.hasDifferences).toBe(true)
    })

    it("应该处理 null 和 undefined", () => {
      const result1 = diffObject(null as any, { a: 1 })
      expect(result1.added).toEqual({ a: 1 })
      expect(result1.removed).toEqual({})
      expect(result1.hasDifferences).toBe(true)

      const result2 = diffObject({ a: 1 }, null as any)
      expect(result2.added).toEqual({})
      expect(result2.removed).toEqual({ a: 1 })
      expect(result2.hasDifferences).toBe(true)

      const result3 = diffObject(null as any, null as any)
      expect(result3.added).toEqual({})
      expect(result3.removed).toEqual({})
      expect(result3.unchanged).toEqual({})
      expect(result3.modified).toEqual({})
      expect(result3.hasDifferences).toBe(false)
    })

    it("应该处理数组值", () => {
      const obj1 = { items: [1, 2, 3], name: "test" }
      const obj2 = { items: [1, 2, 4], name: "test" }
      const result = diffObject(obj1, obj2)

      expect(result.added).toEqual({})
      expect(result.removed).toEqual({})
      expect(result.unchanged).toEqual({ name: "test" })
      expect(result.modified).toEqual({
        items: { oldValue: [1, 2, 3], newValue: [1, 2, 4], path: "items" }
      })
      expect(result.hasDifferences).toBe(true)
    })
  })
})
