import { describe, expect, it } from "vitest"
import { diffArray } from "../diffArray"

describe("diffArray", () => {
  describe("基础类型数组", () => {
    it("应该正确识别数字数组的差异", () => {
      const oldArray = [1, 2, 3]
      const newArray = [2, 3, 4]
      const result = diffArray(oldArray, newArray)

      expect(result.added).toEqual([4])
      expect(result.removed).toEqual([1])
      expect(result.unchanged).toEqual([2, 3])
      expect(result.modified).toEqual([])
    })

    it("应该正确识别字符串数组的差异", () => {
      const oldArray = ["a", "b", "c"]
      const newArray = ["b", "c", "d"]
      const result = diffArray(oldArray, newArray)

      expect(result.added).toEqual(["d"])
      expect(result.removed).toEqual(["a"])
      expect(result.unchanged).toEqual(["b", "c"])
      expect(result.modified).toEqual([])
    })
  })

  describe("对象数组 - 使用 id 字段", () => {
    it("应该正确识别对象数组的差异", () => {
      const oldArray = [
        { id: 1, name: "Alice" },
        { id: 2, name: "Bob" }
      ]
      const newArray = [
        { id: 1, name: "Alice Updated" },
        { id: 3, name: "Charlie" }
      ]
      const result = diffArray(oldArray, newArray, "id")

      expect(result.added).toEqual([{ id: 3, name: "Charlie" }])
      expect(result.removed).toEqual([{ id: 2, name: "Bob" }])
      expect(result.unchanged).toEqual([])
      expect(result.modified).toHaveLength(1)
      expect(result.modified[0].original).toEqual({ id: 1, name: "Alice" })
      expect(result.modified[0].updated).toEqual({ id: 1, name: "Alice Updated" })
      expect(result.modified[0].key).toBe(1)
    })

    it("应该自动使用 id 字段作为默认键", () => {
      const oldArray = [
        { id: 1, name: "Alice" },
        { id: 2, name: "Bob" }
      ]
      const newArray = [
        { id: 1, name: "Alice" },
        { id: 3, name: "Charlie" }
      ]
      const result = diffArray(oldArray, newArray)

      expect(result.added).toEqual([{ id: 3, name: "Charlie" }])
      expect(result.removed).toEqual([{ id: 2, name: "Bob" }])
      expect(result.unchanged).toEqual([{ id: 1, name: "Alice" }])
      expect(result.modified).toEqual([])
    })
  })

  describe("深层路径键", () => {
    it("应该支持深层路径语法", () => {
      const oldArray = [
        { user: { profile: { id: 1 } }, data: "A" },
        { user: { profile: { id: 2 } }, data: "B" }
      ]
      const newArray = [
        { user: { profile: { id: 1 } }, data: "A Updated" },
        { user: { profile: { id: 3 } }, data: "C" }
      ]
      const result = diffArray(oldArray, newArray, "user.profile.id")

      expect(result.added).toEqual([{ user: { profile: { id: 3 } }, data: "C" }])
      expect(result.removed).toEqual([{ user: { profile: { id: 2 } }, data: "B" }])
      expect(result.unchanged).toEqual([])
      expect(result.modified).toHaveLength(1)
      expect(result.modified[0].original).toEqual({ user: { profile: { id: 1 } }, data: "A" })
      expect(result.modified[0].updated).toEqual({ user: { profile: { id: 1 } }, data: "A Updated" })
      expect(result.modified[0].key).toBe(1)
    })
  })

  describe("边界情况", () => {
    it("应该处理空数组", () => {
      const result1 = diffArray([], [1, 2, 3])
      expect(result1.added).toEqual([1, 2, 3])
      expect(result1.removed).toEqual([])
      expect(result1.unchanged).toEqual([])
      expect(result1.modified).toEqual([])

      const result2 = diffArray([1, 2, 3], [])
      expect(result2.added).toEqual([])
      expect(result2.removed).toEqual([1, 2, 3])
      expect(result2.unchanged).toEqual([])
      expect(result2.modified).toEqual([])
    })

    it("应该处理 null 和 undefined", () => {
      const result1 = diffArray(null as any, [1, 2, 3])
      expect(result1.added).toEqual([1, 2, 3])
      expect(result1.removed).toEqual([])

      const result2 = diffArray([1, 2, 3], null as any)
      expect(result2.added).toEqual([])
      expect(result2.removed).toEqual([1, 2, 3])

      const result3 = diffArray(null as any, null as any)
      expect(result3.added).toEqual([])
      expect(result3.removed).toEqual([])
      expect(result3.unchanged).toEqual([])
      expect(result3.modified).toEqual([])
    })

    it("应该处理相同的数组", () => {
      const array = [{ id: 1, name: "Alice" }, { id: 2, name: "Bob" }]
      const result = diffArray(array, array, "id")

      expect(result.added).toEqual([])
      expect(result.removed).toEqual([])
      expect(result.unchanged).toEqual(array)
      expect(result.modified).toEqual([])
    })
  })

  describe("自动键检测", () => {
    it("应该优先使用 id 字段", () => {
      const oldArray = [{ id: 1, key: "a", name: "Alice" }]
      const newArray = [{ id: 1, key: "b", name: "Alice Updated" }]
      const result = diffArray(oldArray, newArray)

      expect(result.modified).toHaveLength(1)
      expect(result.modified[0].key).toBe(1) // 使用 id 而不是 key
    })

    it("应该回退到 key 字段", () => {
      const oldArray = [{ key: "a", name: "Alice" }]
      const newArray = [{ key: "a", name: "Alice Updated" }]
      const result = diffArray(oldArray, newArray)

      expect(result.modified).toHaveLength(1)
      expect(result.modified[0].key).toBe("a")
    })

    it("应该回退到 code 字段", () => {
      const oldArray = [{ code: "USER_001", name: "Alice" }]
      const newArray = [{ code: "USER_001", name: "Alice Updated" }]
      const result = diffArray(oldArray, newArray)

      expect(result.modified).toHaveLength(1)
      expect(result.modified[0].key).toBe("USER_001")
    })

    it("应该回退到 name 字段", () => {
      const oldArray = [{ name: "Alice", age: 25 }]
      const newArray = [{ name: "Alice", age: 26 }]
      const result = diffArray(oldArray, newArray)

      expect(result.modified).toHaveLength(1)
      expect(result.modified[0].key).toBe("Alice")
    })
  })
})
