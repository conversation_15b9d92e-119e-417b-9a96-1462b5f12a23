import { isEqual } from "radash"

/**
 * 数组差异对比结果接口
 */
export interface ArrayDiffResult<T> {
  /** 新增的项目 */
  added: T[]
  /** 删除的项目 */
  removed: T[]
  /** 修改的项目 */
  modified: Array<{
    /** 原始项目 */
    original: T
    /** 修改后的项目 */
    updated: T
    /** 用于匹配的键值 */
    key: string | number
  }>
  /** 未变化的项目 */
  unchanged: T[]
}

/**
 * 数组差异对比函数重载定义
 */
export interface DiffArrayOverloads {
  /**
   * 返回详细差异结果
   */
  <T>(oldArray: T[], newArray: T[], keyPath?: string | string[], returnBool?: false): ArrayDiffResult<T>

  /**
   * 返回布尔值，表示数组是否有差异
   */
  <T>(oldArray: T[], newArray: T[], keyPath: string | string[] | undefined, returnBool: true): boolean
}

/**
 * 获取嵌套对象的值，支持深层路径语法（如 "a.b.c"）
 * @param obj 目标对象
 * @param path 属性路径，支持点分隔的深层路径
 * @returns 属性值，如果路径不存在则返回 undefined
 */
function getNestedValue(obj: any, path: string): any {
  if (!obj || typeof obj !== "object") return undefined

  return path.split(".").reduce((current, key) => {
    return current?.[key]
  }, obj)
}

/**
 * 获取数组项的键值
 * @param item 数组项
 * @param keyPath 可选的键路径，支持深层路径语法或路径数组
 * @returns 键值
 */
function getItemKey(item: any, keyPath?: string | string[]): string | number {
  if (!keyPath) {
    // 如果没有指定 keyPath，尝试使用常见的 ID 字段
    if (typeof item === "object" && item !== null) {
      if (item.id !== undefined) return item.id
      if (item.key !== undefined) return item.key
      if (item.code !== undefined) return item.code
      if (item.name !== undefined) return item.name
    }
    // 对于基础类型，直接返回值本身
    return item
  }

  // 如果 keyPath 是数组，组合多个值作为复合键
  if (Array.isArray(keyPath)) {
    const values = keyPath.map(path => getNestedValue(item, path))
    return values.join("||") // 使用特殊分隔符组合多个值
  }

  return getNestedValue(item, keyPath)
}

/**
 * 对比两个数组的差异，支持基础类型和复杂对象类型
 *
 * @param oldArray 原始数组
 * @param newArray 新数组
 * @param keyPath 可选参数，指定用于对比的 key 值，支持深层路径语法（如 "a.b.c"）或路径数组（如 ["a", "b.c"]）
 * @param returnBool 可选参数，如果为 true 则返回布尔值表示是否有差异，否则返回详细差异结果
 * @returns 差异结果或布尔值
 *
 * @example
 * ```typescript
 * // 基础类型数组
 * const result1 = diffArray([1, 2, 3], [2, 3, 4])
 * // result1.added: [4], result1.removed: [1], result1.unchanged: [2, 3]
 *
 * // 返回布尔值
 * const hasDiff = diffArray([1, 2, 3], [2, 3, 4], undefined, true)
 * // hasDiff: true
 *
 * // 对象数组，指定key
 * const users1 = [{ id: 1, name: 'Alice' }, { id: 2, name: 'Bob' }]
 * const users2 = [{ id: 1, name: 'Alice Updated' }, { id: 3, name: 'Charlie' }]
 * const result2 = diffArray(users1, users2, 'id')
 * // result2.modified: [{ original: { id: 1, name: 'Alice' }, updated: { id: 1, name: 'Alice Updated' }, key: 1 }]
 * // result2.removed: [{ id: 2, name: 'Bob' }]
 * // result2.added: [{ id: 3, name: 'Charlie' }]
 *
 * // 对象数组，深层路径key
 * const items1 = [{ user: { profile: { id: 1 } }, data: 'A' }]
 * const items2 = [{ user: { profile: { id: 1 } }, data: 'B' }]
 * const result3 = diffArray(items1, items2, 'user.profile.id')
 * // result3.modified: [{ original: items1[0], updated: items2[0], key: 1 }]
 *
 * // 复合键（多个字段组合）
 * const items4 = [{ type: 'user', id: 1, name: 'Alice' }]
 * const items5 = [{ type: 'user', id: 1, name: 'Alice Updated' }]
 * const result4 = diffArray(items4, items5, ['type', 'id'])
 * // 使用 type 和 id 的组合作为唯一键进行对比
 * ```
 */

// 函数重载声明
export function diffArray<T>(
  oldArray: T[],
  newArray: T[],
  keyPath?: string | string[],
  returnBool?: false
): ArrayDiffResult<T>

export function diffArray<T>(
  oldArray: T[],
  newArray: T[],
  keyPath: string | string[] | undefined,
  returnBool: true
): boolean

// 函数实现
export function diffArray<T>(
  oldArray: T[],
  newArray: T[],
  keyPath?: string | string[],
  returnBool?: boolean
): ArrayDiffResult<T> | boolean {
  // 边界情况处理
  if (!oldArray && !newArray) {
    const result = { added: [], removed: [], modified: [], unchanged: [] }
    return returnBool ? false : result
  }

  if (!oldArray || oldArray.length === 0) {
    const hasChanges = newArray && newArray.length > 0
    if (returnBool) return hasChanges
    return {
      added: [...(newArray || [])],
      removed: [],
      modified: [],
      unchanged: []
    }
  }

  if (!newArray || newArray.length === 0) {
    const hasChanges = oldArray.length > 0
    if (returnBool) return hasChanges
    return {
      added: [],
      removed: [...oldArray],
      modified: [],
      unchanged: []
    }
  }

  const result: ArrayDiffResult<T> = {
    added: [],
    removed: [],
    modified: [],
    unchanged: []
  }

  // 创建新数组的键值映射
  const newItemsMap = new Map<string | number, T>()
  const newItemKeys = new Set<string | number>()

  newArray.forEach(item => {
    const key = getItemKey(item, keyPath)
    if (key !== undefined && key !== null) {
      newItemsMap.set(key, item)
      newItemKeys.add(key)
    }
  })

  // 创建原数组的键值映射
  const oldItemsMap = new Map<string | number, T>()
  const oldItemKeys = new Set<string | number>()

  oldArray.forEach(item => {
    const key = getItemKey(item, keyPath)
    if (key !== undefined && key !== null) {
      oldItemsMap.set(key, item)
      oldItemKeys.add(key)
    }
  })

  // 查找修改和未变化的项目
  oldItemKeys.forEach(key => {
    const oldItem = oldItemsMap.get(key)!
    const newItem = newItemsMap.get(key)

    if (newItem) {
      // 项目存在于两个数组中，检查是否有变化
      if (isEqual(oldItem, newItem)) {
        result.unchanged.push(oldItem)
      } else {
        result.modified.push({
          original: oldItem,
          updated: newItem,
          key
        })
      }
    } else {
      // 项目只存在于原数组中，标记为删除
      result.removed.push(oldItem)
    }
  })

  // 查找新增的项目
  newItemKeys.forEach(key => {
    if (!oldItemKeys.has(key)) {
      const newItem = newItemsMap.get(key)!
      result.added.push(newItem)
    }
  })

  // 如果需要返回布尔值，检查是否有任何差异
  if (returnBool) {
    return result.added.length > 0 || result.removed.length > 0 || result.modified.length > 0
  }

  return result
}
