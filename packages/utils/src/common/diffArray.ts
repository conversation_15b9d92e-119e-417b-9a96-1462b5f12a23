import { isEqual } from "radash"

/**
 * 数组差异对比结果接口
 */
export interface ArrayDiffResult<T> {
  /** 新增的项目 */
  added: T[]
  /** 删除的项目 */
  removed: T[]
  /** 修改的项目 */
  modified: Array<{
    /** 原始项目 */
    original: T
    /** 修改后的项目 */
    updated: T
    /** 用于匹配的键值 */
    key: string | number
  }>
  /** 未变化的项目 */
  unchanged: T[]
}

/**
 * 获取嵌套对象的值，支持深层路径语法（如 "a.b.c"）
 * @param obj 目标对象
 * @param path 属性路径，支持点分隔的深层路径
 * @returns 属性值，如果路径不存在则返回 undefined
 */
function getNestedValue(obj: any, path: string): any {
  if (!obj || typeof obj !== "object") return undefined
  
  return path.split(".").reduce((current, key) => {
    return current?.[key]
  }, obj)
}

/**
 * 获取数组项的键值
 * @param item 数组项
 * @param keyPath 可选的键路径，支持深层路径语法
 * @returns 键值
 */
function getItemKey(item: any, keyPath?: string): string | number {
  if (!keyPath) {
    // 如果没有指定 keyPath，尝试使用常见的 ID 字段
    if (typeof item === "object" && item !== null) {
      if (item.id !== undefined) return item.id
      if (item.key !== undefined) return item.key
      if (item.code !== undefined) return item.code
      if (item.name !== undefined) return item.name
    }
    // 对于基础类型，直接返回值本身
    return item
  }
  
  return getNestedValue(item, keyPath)
}

/**
 * 对比两个数组的差异，支持基础类型和复杂对象类型
 * 
 * @param oldArray 原始数组
 * @param newArray 新数组
 * @param keyPath 可选参数，指定用于对比的 key 值，支持深层路径语法（如 "a.b.c"）
 * @returns 差异结果，包括新增、删除、修改和未变化的项目
 * 
 * @example
 * ```typescript
 * // 基础类型数组
 * const result1 = diffArray([1, 2, 3], [2, 3, 4])
 * // result1.added: [4], result1.removed: [1], result1.unchanged: [2, 3]
 * 
 * // 对象数组，指定key
 * const users1 = [{ id: 1, name: 'Alice' }, { id: 2, name: 'Bob' }]
 * const users2 = [{ id: 1, name: 'Alice Updated' }, { id: 3, name: 'Charlie' }]
 * const result2 = diffArray(users1, users2, 'id')
 * // result2.modified: [{ original: { id: 1, name: 'Alice' }, updated: { id: 1, name: 'Alice Updated' }, key: 1 }]
 * // result2.removed: [{ id: 2, name: 'Bob' }]
 * // result2.added: [{ id: 3, name: 'Charlie' }]
 * 
 * // 对象数组，深层路径key
 * const items1 = [{ user: { profile: { id: 1 } }, data: 'A' }]
 * const items2 = [{ user: { profile: { id: 1 } }, data: 'B' }]
 * const result3 = diffArray(items1, items2, 'user.profile.id')
 * // result3.modified: [{ original: items1[0], updated: items2[0], key: 1 }]
 * ```
 */
export function diffArray<T>(
  oldArray: T[],
  newArray: T[],
  keyPath?: string
): ArrayDiffResult<T> {
  // 边界情况处理
  if (!oldArray && !newArray) {
    return { added: [], removed: [], modified: [], unchanged: [] }
  }
  
  if (!oldArray || oldArray.length === 0) {
    return {
      added: [...(newArray || [])],
      removed: [],
      modified: [],
      unchanged: []
    }
  }
  
  if (!newArray || newArray.length === 0) {
    return {
      added: [],
      removed: [...oldArray],
      modified: [],
      unchanged: []
    }
  }

  const result: ArrayDiffResult<T> = {
    added: [],
    removed: [],
    modified: [],
    unchanged: []
  }

  // 创建新数组的键值映射
  const newItemsMap = new Map<string | number, T>()
  const newItemKeys = new Set<string | number>()
  
  newArray.forEach(item => {
    const key = getItemKey(item, keyPath)
    if (key !== undefined && key !== null) {
      newItemsMap.set(key, item)
      newItemKeys.add(key)
    }
  })

  // 创建原数组的键值映射
  const oldItemsMap = new Map<string | number, T>()
  const oldItemKeys = new Set<string | number>()
  
  oldArray.forEach(item => {
    const key = getItemKey(item, keyPath)
    if (key !== undefined && key !== null) {
      oldItemsMap.set(key, item)
      oldItemKeys.add(key)
    }
  })

  // 查找修改和未变化的项目
  oldItemKeys.forEach(key => {
    const oldItem = oldItemsMap.get(key)!
    const newItem = newItemsMap.get(key)
    
    if (newItem) {
      // 项目存在于两个数组中，检查是否有变化
      if (isEqual(oldItem, newItem)) {
        result.unchanged.push(oldItem)
      } else {
        result.modified.push({
          original: oldItem,
          updated: newItem,
          key
        })
      }
    } else {
      // 项目只存在于原数组中，标记为删除
      result.removed.push(oldItem)
    }
  })

  // 查找新增的项目
  newItemKeys.forEach(key => {
    if (!oldItemKeys.has(key)) {
      const newItem = newItemsMap.get(key)!
      result.added.push(newItem)
    }
  })

  return result
}
